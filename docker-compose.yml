version: '3.8'

services:
  # Crawl4AI Docker service for async job processing
  crawl4ai:
    image: unclecode/crawl4ai:latest
    container_name: crawl4ai-service
    ports:
      - "11235:11235"
    environment:
      - MAX_CONCURRENT_SESSIONS=10
      - MEMORY_THRESHOLD_PERCENT=80
      - CRAWL4AI__MAX_CONCURRENT=10
      - CRAWL4AI__HEADLESS=true
    shm_size: 2g
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11235/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2'
    networks:
      - rag-network

  # AY RAG MCP Server
  ay-rag-mcp:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - PORT=8051
    container_name: ay-rag-mcp-server
    ports:
      - "8051:8051"
    environment:
      # Core MCP Configuration (from .env)
      - HOST=${HOST:-0.0.0.0}
      - PORT=8051
      - TRANSPORT=${TRANSPORT:-sse}
      
      # Docker Integration (from .env)
      - CRAWL4AI_DOCKER_HOST=${CRAWL4AI_DOCKER_HOST:-http://crawl4ai:11235}
      
      # LLM Provider Configuration (from .env)
      - LLM_PROVIDER=${LLM_PROVIDER:-openai}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      
      # Model Selection (from .env)
      - CONTEXTUAL_EMBEDDING_MODEL=${CONTEXTUAL_EMBEDDING_MODEL:-gpt-4o-mini}
      - CODE_ANALYSIS_MODEL=${CODE_ANALYSIS_MODEL:-gpt-4o-mini}
      - QUERY_ENHANCEMENT_MODEL=${QUERY_ENHANCEMENT_MODEL:-gpt-4o-mini}
      
      # Legacy support (from .env)
      - MODEL_CHOICE=${MODEL_CHOICE:-gpt-4o-mini}
      
      # Supabase Configuration (from .env)
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      
      # RAG Strategy Flags (from .env)
      - USE_CONTEXTUAL_EMBEDDINGS=${USE_CONTEXTUAL_EMBEDDINGS:-false}
      - USE_HYBRID_SEARCH=${USE_HYBRID_SEARCH:-true}
      - USE_AGENTIC_RAG=${USE_AGENTIC_RAG:-false}
      - USE_RERANKING=${USE_RERANKING:-false}
      
      # Code Detection Configuration (from .env)
      - MIN_CODE_LENGTH=${MIN_CODE_LENGTH:-50}
      
      # GitHub Integration (from .env)
      - GITHUB_TOKEN=${GITHUB_TOKEN}
      
      # Resilience Settings (from .env)
      - CIRCUIT_BREAKER_THRESHOLD=${CIRCUIT_BREAKER_THRESHOLD:-3}
      - CIRCUIT_BREAKER_TIMEOUT=${CIRCUIT_BREAKER_TIMEOUT:-60}
      - USE_QUERY_EXPANSION=${USE_QUERY_EXPANSION:-true}
      
      # Performance Settings (from .env)
      - DEFAULT_BATCH_SIZE=${DEFAULT_BATCH_SIZE:-50}
    depends_on:
      crawl4ai:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8051/ready"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 180s  # Extended time for complex MCP initialization in containers
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1'
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 120s
    networks:
      - rag-network
    volumes:
      # Optional: Mount local directory for persistent logs
      - ./logs:/app/logs


  # Redis (Optional - for caching and session storage)
  redis:
    image: redis:7-alpine
    container_name: rag-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - rag-network
    volumes:
      - redis-data:/data

networks:
  rag-network:
    driver: bridge

volumes:
  redis-data:
    driver: local

