#!/usr/bin/env python3
"""
TUI Interaction Testing Script

This script tests TUI menu interactions and event handling.
"""

import sys
import os
import asyncio
from pathlib import Path
from unittest.mock import Mock, patch

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_home_screen_buttons():
    """Test home screen button event handlers."""
    print("🧪 Testing home screen button interactions...")
    
    try:
        from tui.screens.home import HomeScreen
        from tui.app import AYKnowledgeBaseTUI
        
        # Create mock app
        mock_app = Mock(spec=AYKnowledgeBaseTUI)
        
        # Create home screen
        home_screen = HomeScreen()
        home_screen.app = mock_app
        
        # Test button handlers exist
        handlers = [
            'handle_crawl_url',
            'handle_crawl_search',
            'handle_chat',
            'handle_sources',
            'handle_settings',
            'handle_help'
        ]
        
        for handler_name in handlers:
            if hasattr(home_screen, handler_name):
                print(f"✅ Handler '{handler_name}' exists")
                
                # Test handler can be called
                handler = getattr(home_screen, handler_name)
                try:
                    handler()
                    print(f"✅ Handler '{handler_name}' callable")
                except Exception as e:
                    print(f"⚠️  Handler '{handler_name}' callable but may need app context: {e}")
            else:
                print(f"❌ Handler '{handler_name}' missing")
                return False
        
        return True
    except Exception as e:
        print(f"❌ Home screen button test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_screen_navigation():
    """Test that navigation methods work correctly."""
    print("\n🧪 Testing screen navigation...")
    
    try:
        from tui.app import AYKnowledgeBaseTUI
        from tui.screens.home import HomeScreen
        from tui.screens.crawl_url import CrawlURLScreen
        from tui.screens.chat import ChatScreen
        
        # Create app
        app = AYKnowledgeBaseTUI()
        
        # Mock the push_screen method to avoid actual screen changes
        app.push_screen = Mock()
        
        # Test navigation methods
        navigation_tests = [
            ('show_crawl_url', CrawlURLScreen),
            ('show_crawl_search', 'CrawlSearchScreen'),
            ('show_chat', ChatScreen),
            ('show_sources', 'SourcesScreen'),
            ('show_settings', 'SettingsScreen'),
            ('show_help', 'HelpScreen')
        ]
        
        for method_name, expected_screen in navigation_tests:
            method = getattr(app, method_name)
            method()
            print(f"✅ Navigation method '{method_name}' executed successfully")
            
            # Verify push_screen was called
            if app.push_screen.called:
                print(f"✅ '{method_name}' calls push_screen")
                app.push_screen.reset_mock()
            else:
                print(f"❌ '{method_name}' doesn't call push_screen")
                return False
        
        return True
    except Exception as e:
        print(f"❌ Screen navigation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_widget_composition():
    """Test that screens compose widgets correctly."""
    print("\n🧪 Testing widget composition...")
    
    try:
        from tui.screens.home import HomeScreen
        from tui.screens.crawl_url import CrawlURLScreen
        from tui.screens.chat import ChatScreen
        
        screens_to_test = [
            ("Home", HomeScreen),
            ("CrawlURL", CrawlURLScreen),
            ("Chat", ChatScreen)
        ]
        
        for screen_name, screen_class in screens_to_test:
            screen = screen_class()
            
            # Test compose method returns something
            try:
                compose_result = screen.compose()
                if compose_result is not None:
                    print(f"✅ {screen_name} screen compose() returns widgets")
                    
                    # Try to iterate through the result
                    widget_count = 0
                    for widget in compose_result:
                        widget_count += 1
                    print(f"✅ {screen_name} screen composes {widget_count} widgets")
                else:
                    print(f"⚠️  {screen_name} screen compose() returns None")
            except Exception as e:
                print(f"❌ {screen_name} screen compose() failed: {e}")
                return False
        
        return True
    except Exception as e:
        print(f"❌ Widget composition test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_css_and_styling():
    """Test that screens have proper CSS styling."""
    print("\n🧪 Testing CSS and styling...")
    
    try:
        from tui.screens.home import HomeScreen
        from tui.screens.crawl_url import CrawlURLScreen
        from tui.screens.chat import ChatScreen
        from tui.app import AYKnowledgeBaseTUI
        
        # Test main app CSS
        app = AYKnowledgeBaseTUI()
        if hasattr(app, 'CSS') and app.CSS:
            print("✅ Main app has CSS styling")
        else:
            print("⚠️  Main app missing CSS styling")
        
        # Test screen CSS
        screens_to_test = [
            ("Home", HomeScreen),
            ("CrawlURL", CrawlURLScreen),
            ("Chat", ChatScreen)
        ]
        
        for screen_name, screen_class in screens_to_test:
            screen = screen_class()
            if hasattr(screen, 'CSS') and screen.CSS:
                print(f"✅ {screen_name} screen has CSS styling")
            else:
                print(f"⚠️  {screen_name} screen missing CSS styling")
        
        return True
    except Exception as e:
        print(f"❌ CSS and styling test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_key_bindings():
    """Test that key bindings are properly configured."""
    print("\n🧪 Testing key bindings...")
    
    try:
        from tui.app import AYKnowledgeBaseTUI
        
        app = AYKnowledgeBaseTUI()
        
        # Check for key bindings
        if hasattr(app, 'BINDINGS') and app.BINDINGS:
            print(f"✅ App has {len(app.BINDINGS)} key bindings")
            
            for binding in app.BINDINGS:
                print(f"✅ Key binding: {binding.key} -> {binding.action}")
        else:
            print("⚠️  App missing key bindings")
        
        # Test action methods exist
        action_methods = ['action_go_home', 'action_quit']
        for method_name in action_methods:
            if hasattr(app, method_name):
                print(f"✅ Action method '{method_name}' exists")
            else:
                print(f"❌ Action method '{method_name}' missing")
                return False
        
        return True
    except Exception as e:
        print(f"❌ Key bindings test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all interaction tests."""
    print("🚀 Starting TUI Interaction Testing\n")
    
    tests = [
        ("Home Screen Button Tests", test_home_screen_buttons),
        ("Screen Navigation Tests", test_screen_navigation),
        ("Widget Composition Tests", test_widget_composition),
        ("CSS and Styling Tests", test_css_and_styling),
        ("Key Bindings Tests", test_key_bindings)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("INTERACTION TEST SUMMARY")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} interaction tests passed")
    
    if passed == total:
        print("🎉 All interaction tests passed! TUI should be fully functional.")
    else:
        print("⚠️  Some interaction tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
