# Product Overview

AY RAG MCP Server is a Model Context Protocol (MCP) implementation that provides AI agents and coding assistants with advanced web crawling and RAG (Retrieval-Augmented Generation) capabilities.

## Core Purpose
- Enable AI agents to crawl websites and store content in vector databases
- Provide intelligent search and retrieval over crawled content
- Support AI coding assistants with specialized code example extraction
- Detect AI hallucinations through knowledge graph validation

## Key Features
- **Intelligent Routing**: Automatically chooses optimal crawling method based on URL type
- **Advanced RAG Strategies**: Contextual embeddings, hybrid search, agentic RAG, reranking
- **Knowledge Graph Integration**: Neo4j-based repository analysis and hallucination detection
- **HTMX Dashboard**: Web interface for monitoring and interaction
- **Docker Integration**: Containerized deployment with Crawl4AI service

## Target Users
- AI coding assistants (primary integration target: Archon V2)
- Developers building MCP-enabled applications
- Teams needing automated documentation crawling and search

## Architecture
- MCP server with FastMCP framework
- Supabase for vector storage and RAG
- Crawl4AI for web crawling
- Optional Neo4j for knowledge graphs
- HTMX-based dashboard for web interface