# Project Structure

## Root Directory Organization

### Core Application
- `src/` - Main MCP server source code
  - `ay_rag_mcp.py` - Main MCP server entry point
  - `utils.py` - Shared utility functions
  - `*_client.py` - External service clients (Docker, GitHub)
  - `*_handler.py` - Error handling and monitoring
  - `*_filter.py` - Content filtering and validation
  - `*_monitor.py` - Performance monitoring

### Dashboard Application
- `dashboard/` - HTMX web dashboard
  - `main.py` - FastAPI application entry point
  - `config.py` - Configuration management
  - `routes/` - API route handlers
  - `templates/` - Jinja2 HTML templates
    - `base.html` - Base template
    - `components/` - Reusable UI components
    - `pages/` - Full page templates
    - `partials/` - HTMX partial templates
  - `static/css/` - Stylesheets
  - `middleware.py` - Custom middleware

### Testing
- `tests/` - Test suite
  - `test_*_integration.py` - Integration tests
  - `test_*.py` - Unit tests
- `test_*.py` - Root level test scripts

### Configuration & Deployment
- `.env` / `.env.example` - Environment configuration
- `docker-compose.yml` - Multi-service orchestration
- `Dockerfile` / `Dockerfile.dashboard` - Container definitions
- `setup.py` - Python package configuration
- `uv.lock` - Dependency lock file

### Documentation
- `README.md` - Main project documentation
- `*.md` - Feature-specific documentation
- `docs/` - Additional documentation
  - `deprecated/` - Archived documentation

### Database
- `crawled_pages.sql` - Database schema
- `database_schema_update.sql` - Schema migrations

## Code Organization Patterns

### MCP Server Structure
- Main server in `src/ay_rag_mcp.py` with FastMCP framework
- Tool functions decorated with `@mcp.tool()`
- Async/await patterns throughout
- Dependency injection via lifespan context

### Dashboard Structure
- FastAPI application with route modules
- HTMX for dynamic frontend interactions
- Component-based template organization
- Middleware for cross-cutting concerns

### Utility Organization
- Client classes for external services
- Error handling with circuit breaker patterns
- Performance monitoring and metrics
- Content validation and filtering

## File Naming Conventions
- Snake_case for Python files
- Descriptive prefixes for related functionality
- `test_` prefix for test files
- Component-based organization in templates

## Import Patterns
- Relative imports within modules
- External dependencies imported at module level
- Optional imports with try/catch for features
- Environment-based conditional imports