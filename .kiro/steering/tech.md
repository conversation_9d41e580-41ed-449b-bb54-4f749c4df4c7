# Technology Stack

## Core Technologies
- **Python 3.12+**: Primary language
- **FastMCP**: MCP server framework
- **FastAPI**: Web framework for dashboard
- **Crawl4AI 0.6.2**: Web crawling engine
- **Supabase**: Vector database and storage
- **OpenAI API**: Embeddings and LLM processing

## Build System
- **uv**: Python package manager and virtual environment
- **setuptools**: Package building
- **Docker**: Containerization
- **Docker Compose**: Multi-service orchestration

## Key Dependencies
- `mcp==1.7.1`: Model Context Protocol implementation
- `supabase==2.15.1`: Database client
- `openai==1.71.0`: AI API client
- `sentence-transformers>=4.1.0`: Reranking models
- `jinja2>=3.1.2`: Template engine (dashboard)
- `httpx>=0.25.0`: HTTP client

## Optional Components
- **Neo4j**: Knowledge graph database
- **Redis**: Caching layer
- **HTMX**: Frontend interactivity

## Common Commands

### Development Setup
```bash
# Install uv package manager
pip install uv

# Create virtual environment
uv venv
source .venv/bin/activate  # Linux/Mac
# .venv\Scripts\activate   # Windows

# Install dependencies
uv pip install -e .
crawl4ai-setup
```

### Running Services
```bash
# Run MCP server directly
uv run src/ay_rag_mcp.py

# Run dashboard
python run_dashboard.py

# Docker build
docker build -t ay/rag-mcp --build-arg PORT=8051 .

# Docker Compose (full stack)
docker compose up -d
docker compose logs -f
docker compose down
```

### Testing
```bash
# Run tests
pytest tests/
python -m pytest tests/test_basic_integration.py

# Integration tests
python test_implementation.py
python test_concurrent_sse.py
```

### Database Setup
```bash
# Apply schema (run in Supabase SQL editor)
# Execute contents of crawled_pages.sql
```

## Environment Configuration
- `.env` file required with API keys and database URLs
- See `.env.example` for template
- Docker uses environment variable passthrough