#!/usr/bin/env python3
"""
TUI Menu Testing Script

This script tests all TUI menu functionality to identify and fix issues.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test all TUI imports."""
    print("🧪 Testing TUI imports...")
    
    try:
        from tui.app import AYKnowledgeBaseTUI
        print("✅ Main app import successful")
    except Exception as e:
        print(f"❌ Main app import failed: {e}")
        return False
    
    try:
        from tui.screens.home import HomeScreen
        print("✅ Home screen import successful")
    except Exception as e:
        print(f"❌ Home screen import failed: {e}")
        return False
    
    try:
        from tui.screens.crawl_url import CrawlURLScreen
        print("✅ Crawl URL screen import successful")
    except Exception as e:
        print(f"❌ Crawl URL screen import failed: {e}")
        return False
    
    try:
        from tui.screens.crawl_search import CrawlSearchScreen
        print("✅ Crawl Search screen import successful")
    except Exception as e:
        print(f"❌ Crawl Search screen import failed: {e}")
        return False
    
    try:
        from tui.screens.chat import ChatScreen
        print("✅ Chat screen import successful")
    except Exception as e:
        print(f"❌ Chat screen import failed: {e}")
        return False
    
    try:
        from tui.screens.sources import SourcesScreen
        print("✅ Sources screen import successful")
    except Exception as e:
        print(f"❌ Sources screen import failed: {e}")
        return False
    
    try:
        from tui.screens.settings import SettingsScreen
        print("✅ Settings screen import successful")
    except Exception as e:
        print(f"❌ Settings screen import failed: {e}")
        return False
    
    try:
        from tui.screens.help import HelpScreen
        print("✅ Help screen import successful")
    except Exception as e:
        print(f"❌ Help screen import failed: {e}")
        return False
    
    return True

def test_mcp_client():
    """Test MCP client functionality."""
    print("\n🧪 Testing MCP client...")
    
    try:
        from tui.mcp_client import MCPClient, get_mcp_client
        print("✅ MCP client import successful")
        
        # Test client creation
        client = get_mcp_client()
        print("✅ MCP client creation successful")
        
        # Test connection (should handle failure gracefully)
        connected = client.test_connection()
        if connected:
            print("✅ MCP server connection successful")
        else:
            print("⚠️  MCP server not available (expected in test environment)")
        
        return True
    except Exception as e:
        print(f"❌ MCP client test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_app_creation():
    """Test TUI app creation and basic functionality."""
    print("\n🧪 Testing TUI app creation...")
    
    try:
        from tui.app import AYKnowledgeBaseTUI
        
        # Create app instance
        app = AYKnowledgeBaseTUI()
        print("✅ TUI app creation successful")
        
        # Test navigation methods exist
        methods_to_test = [
            'show_crawl_url',
            'show_crawl_search', 
            'show_chat',
            'show_sources',
            'show_settings',
            'show_help'
        ]
        
        for method_name in methods_to_test:
            if hasattr(app, method_name):
                print(f"✅ Navigation method '{method_name}' exists")
            else:
                print(f"❌ Navigation method '{method_name}' missing")
                return False
        
        # Test connection status method
        status = app.get_connection_status()
        print(f"✅ Connection status: {status}")
        
        return True
    except Exception as e:
        print(f"❌ TUI app creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_screen_composition():
    """Test that all screens can be composed without errors."""
    print("\n🧪 Testing screen composition...")
    
    try:
        from tui.screens.home import HomeScreen
        from tui.screens.crawl_url import CrawlURLScreen
        from tui.screens.crawl_search import CrawlSearchScreen
        from tui.screens.chat import ChatScreen
        from tui.screens.sources import SourcesScreen
        from tui.screens.settings import SettingsScreen
        from tui.screens.help import HelpScreen
        
        screens = [
            ("Home", HomeScreen),
            ("CrawlURL", CrawlURLScreen),
            ("CrawlSearch", CrawlSearchScreen),
            ("Chat", ChatScreen),
            ("Sources", SourcesScreen),
            ("Settings", SettingsScreen),
            ("Help", HelpScreen)
        ]
        
        for name, screen_class in screens:
            try:
                screen = screen_class()
                print(f"✅ {name} screen creation successful")
                
                # Test compose method exists
                if hasattr(screen, 'compose'):
                    print(f"✅ {name} screen has compose method")
                else:
                    print(f"❌ {name} screen missing compose method")
                    return False
                    
            except Exception as e:
                print(f"❌ {name} screen creation failed: {e}")
                return False
        
        return True
    except Exception as e:
        print(f"❌ Screen composition test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Starting TUI Menu Testing\n")
    
    tests = [
        ("Import Tests", test_imports),
        ("MCP Client Tests", test_mcp_client),
        ("App Creation Tests", test_app_creation),
        ("Screen Composition Tests", test_screen_composition)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! TUI menus should be functional.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
