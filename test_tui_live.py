#!/usr/bin/env python3
"""
Live TUI Testing Script

This script actually runs the TUI briefly to test functionality.
"""

import sys
import os
import asyncio
import signal
from pathlib import Path
from threading import Timer

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_tui_startup():
    """Test that TUI can start up successfully."""
    print("🧪 Testing TUI startup...")
    
    try:
        from tui.app import AYKnowledgeBaseTUI
        
        # Create app
        app = AYKnowledgeBaseTUI()
        print("✅ TUI app created successfully")
        
        # Test that we can access the main components
        print(f"✅ App title: {app.TITLE}")
        print(f"✅ App subtitle: {app.SUB_TITLE}")
        print(f"✅ App has {len(app.BINDINGS)} key bindings")
        
        # Test connection status
        status = app.get_connection_status()
        print(f"✅ Connection status available: {status}")
        
        return True
    except Exception as e:
        print(f"❌ TUI startup test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_screen_imports():
    """Test that all screens can be imported and instantiated."""
    print("\n🧪 Testing screen imports and instantiation...")
    
    screens = [
        ("HomeScreen", "tui.screens.home", "HomeScreen"),
        ("CrawlURLScreen", "tui.screens.crawl_url", "CrawlURLScreen"),
        ("CrawlSearchScreen", "tui.screens.crawl_search", "CrawlSearchScreen"),
        ("ChatScreen", "tui.screens.chat", "ChatScreen"),
        ("SourcesScreen", "tui.screens.sources", "SourcesScreen"),
        ("SettingsScreen", "tui.screens.settings", "SettingsScreen"),
        ("HelpScreen", "tui.screens.help", "HelpScreen")
    ]
    
    for screen_name, module_name, class_name in screens:
        try:
            module = __import__(module_name, fromlist=[class_name])
            screen_class = getattr(module, class_name)
            screen_instance = screen_class()
            print(f"✅ {screen_name} imported and instantiated successfully")
        except Exception as e:
            print(f"❌ {screen_name} failed: {e}")
            return False
    
    return True

def test_mcp_integration():
    """Test MCP client integration."""
    print("\n🧪 Testing MCP integration...")
    
    try:
        from tui.mcp_client import get_mcp_client, get_available_sources
        
        # Test client creation
        client = get_mcp_client()
        print("✅ MCP client created successfully")
        
        # Test connection (should handle failure gracefully)
        try:
            connected = client.test_connection()
            if connected:
                print("✅ MCP server connection successful")
            else:
                print("⚠️  MCP server not available (expected in test environment)")
        except Exception as e:
            print(f"⚠️  MCP connection test failed (expected): {e}")
        
        # Test function availability
        try:
            result = get_available_sources()
            print("✅ get_available_sources() function available")
        except Exception as e:
            print(f"⚠️  get_available_sources() failed (expected without server): {e}")
        
        return True
    except Exception as e:
        print(f"❌ MCP integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_app_methods():
    """Test that all required app methods exist and are callable."""
    print("\n🧪 Testing app methods...")
    
    try:
        from tui.app import AYKnowledgeBaseTUI
        
        app = AYKnowledgeBaseTUI()
        
        # Test navigation methods
        navigation_methods = [
            'show_crawl_url',
            'show_crawl_search',
            'show_chat',
            'show_sources',
            'show_settings',
            'show_help'
        ]
        
        for method_name in navigation_methods:
            if hasattr(app, method_name):
                method = getattr(app, method_name)
                if callable(method):
                    print(f"✅ Method '{method_name}' exists and is callable")
                else:
                    print(f"❌ Method '{method_name}' exists but is not callable")
                    return False
            else:
                print(f"❌ Method '{method_name}' missing")
                return False
        
        # Test action methods
        action_methods = ['action_go_home', 'action_quit']
        for method_name in action_methods:
            if hasattr(app, method_name):
                method = getattr(app, method_name)
                if callable(method):
                    print(f"✅ Action '{method_name}' exists and is callable")
                else:
                    print(f"❌ Action '{method_name}' exists but is not callable")
                    return False
            else:
                print(f"❌ Action '{method_name}' missing")
                return False
        
        # Test utility methods
        utility_methods = ['get_connection_status', 'check_connections']
        for method_name in utility_methods:
            if hasattr(app, method_name):
                method = getattr(app, method_name)
                if callable(method):
                    print(f"✅ Utility '{method_name}' exists and is callable")
                else:
                    print(f"❌ Utility '{method_name}' exists but is not callable")
                    return False
            else:
                print(f"❌ Utility '{method_name}' missing")
                return False
        
        return True
    except Exception as e:
        print(f"❌ App methods test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_css_styling():
    """Test CSS styling is properly defined."""
    print("\n🧪 Testing CSS styling...")
    
    try:
        from tui.app import AYKnowledgeBaseTUI
        from tui.screens.home import HomeScreen
        
        # Test main app CSS
        app = AYKnowledgeBaseTUI()
        if hasattr(app, 'CSS') and app.CSS and len(app.CSS.strip()) > 0:
            print("✅ Main app has CSS styling defined")
        else:
            print("❌ Main app missing CSS styling")
            return False
        
        # Test home screen CSS
        home_screen = HomeScreen()
        if hasattr(home_screen, 'CSS') and home_screen.CSS and len(home_screen.CSS.strip()) > 0:
            print("✅ Home screen has CSS styling defined")
        else:
            print("❌ Home screen missing CSS styling")
            return False
        
        return True
    except Exception as e:
        print(f"❌ CSS styling test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all live tests."""
    print("🚀 Starting Live TUI Testing\n")
    
    tests = [
        ("TUI Startup Test", test_tui_startup),
        ("Screen Imports Test", test_screen_imports),
        ("MCP Integration Test", test_mcp_integration),
        ("App Methods Test", test_app_methods),
        ("CSS Styling Test", test_css_styling)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("LIVE TEST SUMMARY")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} live tests passed")
    
    if passed == total:
        print("🎉 All live tests passed! TUI is ready to use.")
        print("\n📋 To run the TUI:")
        print("   source tui_venv/bin/activate")
        print("   python -m src.tui.app")
    else:
        print("⚠️  Some live tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
