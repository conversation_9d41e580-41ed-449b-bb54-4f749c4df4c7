"""
Utility functions for the AY RAG MCP server.
Enhanced with async capabilities for improved performance.
"""
import os
import asyncio
import concurrent.futures
from typing import List, Dict, Any, Optional, Tuple
import json
from supabase import create_client, Client
from urllib.parse import urlparse
import openai
import re
import time
import logging

logger = logging.getLogger(__name__)

# Import new LLM abstraction layer
try:
    from .llm_client import get_default_llm_client
    from .model_config import get_default_model_config
except ImportError:
    # Handle both relative and absolute imports
    from llm_client import get_default_llm_client
    from model_config import get_default_model_config

# Load OpenAI API key for embeddings (backward compatibility)
openai.api_key = os.getenv("OPENAI_API_KEY")

def get_supabase_client() -> Client:
    """
    Get a Supabase client with the URL and key from environment variables.
    
    Returns:
        Supabase client instance
    """
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY")
    
    if not url or not key:
        raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set in environment variables")
    
    return create_client(url, key)

def create_embeddings_batch(texts: List[str]) -> List[List[float]]:
    """
    Create embeddings for multiple texts in a single API call.
    
    Args:
        texts: List of texts to create embeddings for
        
    Returns:
        List of embeddings (each embedding is a list of floats)
    """
    if not texts:
        return []
    
    max_retries = 3
    retry_delay = 1.0  # Start with 1 second delay
    
    for retry in range(max_retries):
        try:
            response = openai.embeddings.create(
                model="text-embedding-3-small", # Hardcoding embedding model for now, will change this later to be more dynamic
                input=texts
            )
            return [item.embedding for item in response.data]
        except Exception as e:
            if retry < max_retries - 1:
                print(f"Error creating batch embeddings (attempt {retry + 1}/{max_retries}): {e}")
                print(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
            else:
                print(f"Failed to create batch embeddings after {max_retries} attempts: {e}")
                # Try creating embeddings one by one as fallback
                print("Attempting to create embeddings individually...")
                embeddings = []
                successful_count = 0
                
                for i, text in enumerate(texts):
                    try:
                        individual_response = openai.embeddings.create(
                            model="text-embedding-3-small",
                            input=[text]
                        )
                        embeddings.append(individual_response.data[0].embedding)
                        successful_count += 1
                    except Exception as individual_error:
                        print(f"Failed to create embedding for text {i}: {individual_error}")
                        # Add zero embedding as fallback
                        embeddings.append([0.0] * 1536)
                
                print(f"Successfully created {successful_count}/{len(texts)} embeddings individually")
                return embeddings

def create_embedding(text: str) -> List[float]:
    """
    Create an embedding for a single text using OpenAI's API.
    
    Args:
        text: Text to create an embedding for
        
    Returns:
        List of floats representing the embedding
    """
    try:
        embeddings = create_embeddings_batch([text])
        return embeddings[0] if embeddings else [0.0] * 1536
    except Exception as e:
        print(f"Error creating embedding: {e}")
        # Return empty embedding if there's an error
        return [0.0] * 1536

def generate_contextual_embedding(full_document: str, chunk: str) -> Tuple[str, bool]:
    """
    Generate contextual information for a chunk within a document to improve retrieval.
    (Sync version - returns original chunk without LLM enhancement)
    
    Args:
        full_document: The complete document text
        chunk: The specific chunk of text to generate context for
        
    Returns:
        Tuple containing:
        - The original chunk text
        - Boolean indicating if contextual embedding was performed (always False in sync version)
    """
    try:
        # In sync version, return the original chunk without LLM enhancement
        # This maintains functionality while removing async dependencies
        return chunk, False
    
    except Exception as e:
        print(f"Error in contextual embedding: {e}. Using original chunk instead.")
        return chunk, False


def process_chunk_with_context(args):
    """
    Process a single chunk with contextual embedding (sync wrapper).
    This function is designed to be used with concurrent.futures.
    
    Args:
        args: Tuple containing (url, content, full_document)
        
    Returns:
        Tuple containing:
        - The contextual text that situates the chunk within the document
        - Boolean indicating if contextual embedding was performed
    """
    try:
        url, content, full_document = args
        # Use fallback for thread executor - just return original content
        return content, False
    except Exception as e:
        print(f"Error in process_chunk_with_context: {e}")
        url, content, full_document = args
        return content, False

def add_documents_to_supabase(
    client: Client, 
    urls: List[str], 
    chunk_numbers: List[int],
    contents: List[str], 
    metadatas: List[Dict[str, Any]],
    url_to_full_document: Dict[str, str],
    batch_size: int = 20
) -> None:
    """
    Add documents to the Supabase crawled_pages table in batches.
    Deletes existing records with the same URLs before inserting to prevent duplicates.
    
    Args:
        client: Supabase client
        urls: List of URLs
        chunk_numbers: List of chunk numbers
        contents: List of document contents
        metadatas: List of document metadata
        url_to_full_document: Dictionary mapping URLs to their full document content
        batch_size: Size of each batch for insertion
    """
    # Get unique URLs to delete existing records
    unique_urls = list(set(urls))
    
    # Delete existing records for these URLs in a single operation
    try:
        if unique_urls:
            # Use the .in_() filter to delete all records with matching URLs
            client.table("crawled_pages").delete().in_("url", unique_urls).execute()
    except Exception as e:
        print(f"Batch delete failed: {e}. Trying one-by-one deletion as fallback.")
        # Fallback: delete records one by one
        for url in unique_urls:
            try:
                client.table("crawled_pages").delete().eq("url", url).execute()
            except Exception as inner_e:
                print(f"Error deleting record for URL {url}: {inner_e}")
                # Continue with the next URL even if one fails
    
    # Check if MODEL_CHOICE is set for contextual embeddings
    use_contextual_embeddings = os.getenv("USE_CONTEXTUAL_EMBEDDINGS", "false") == "true"
    print(f"\n\nUse contextual embeddings: {use_contextual_embeddings}\n\n")
    
    # Process in batches to avoid memory issues
    for i in range(0, len(contents), batch_size):
        batch_end = min(i + batch_size, len(contents))
        
        # Get batch slices
        batch_urls = urls[i:batch_end]
        batch_chunk_numbers = chunk_numbers[i:batch_end]
        batch_contents = contents[i:batch_end]
        batch_metadatas = metadatas[i:batch_end]
        
        # Apply contextual embedding to each chunk if MODEL_CHOICE is set
        if use_contextual_embeddings:
            # Prepare arguments for parallel processing
            process_args = []
            for j, content in enumerate(batch_contents):
                url = batch_urls[j]
                full_document = url_to_full_document.get(url, "")
                process_args.append((url, content, full_document))
            
            # Process in parallel using ThreadPoolExecutor
            contextual_contents = []
            with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
                # Submit all tasks and collect results
                future_to_idx = {executor.submit(process_chunk_with_context, arg): idx 
                                for idx, arg in enumerate(process_args)}
                
                # Process results as they complete
                for future in concurrent.futures.as_completed(future_to_idx):
                    idx = future_to_idx[future]
                    try:
                        result, success = future.result()
                        contextual_contents.append(result)
                        if success:
                            batch_metadatas[idx]["contextual_embedding"] = True
                    except Exception as e:
                        print(f"Error processing chunk {idx}: {e}")
                        # Use original content as fallback
                        contextual_contents.append(batch_contents[idx])
            
            # Sort results back into original order if needed
            if len(contextual_contents) != len(batch_contents):
                print(f"Warning: Expected {len(batch_contents)} results but got {len(contextual_contents)}")
                # Use original contents as fallback
                contextual_contents = batch_contents
        else:
            # If not using contextual embeddings, use original contents
            contextual_contents = batch_contents
        
        # Create embeddings for the entire batch at once
        print(f"🧠 Creating embeddings for batch of {len(contextual_contents)} chunks...")
        print("💡 Hint: Large batches improve efficiency but use more memory. Adjust batch_size if needed.")
        
        # Try async embeddings first, fallback to sync if needed
        try:
            batch_embeddings = create_embeddings_batch(contextual_contents)
            print("✅ Used async embeddings for improved performance")
        except Exception as e:
            print(f"⚠️ Async embeddings failed, using sync fallback: {e}")
            batch_embeddings = create_embeddings_batch(contextual_contents)
        
        batch_data = []
        for j in range(len(contextual_contents)):
            # Extract metadata fields
            chunk_size = len(contextual_contents[j])
            
            # Extract source_id from URL
            parsed_url = urlparse(batch_urls[j])
            source_id = parsed_url.netloc or parsed_url.path
            
            # Prepare data for insertion
            data = {
                "url": batch_urls[j],
                "chunk_number": batch_chunk_numbers[j],
                "content": contextual_contents[j],  # Store original content
                "metadata": {
                    "chunk_size": chunk_size,
                    **batch_metadatas[j]
                },
                "source_id": source_id,  # Add source_id field
                "embedding": batch_embeddings[j]  # Use embedding from contextual content
            }
            
            batch_data.append(data)
        
        # Insert batch into Supabase with retry logic
        print(f"💾 Inserting batch of {len(batch_data)} records into database...")
        print("💡 Hint: Using exponential backoff for retries to handle rate limits gracefully")
        max_retries = 3
        retry_delay = 1.0  # Start with 1 second delay
        
        for retry in range(max_retries):
            try:
                client.table("crawled_pages").insert(batch_data).execute()
                # Success - break out of retry loop
                print(f"✅ Successfully inserted batch of {len(batch_data)} records")
                break
            except Exception as e:
                if retry < max_retries - 1:
                    print(f"⚠️  Error inserting batch into Supabase (attempt {retry + 1}/{max_retries}): {e}")
                    print(f"💡 Hint: Retrying in {retry_delay} seconds with exponential backoff...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    # Final attempt failed
                    print(f"❌ Failed to insert batch after {max_retries} attempts: {e}")
                    # Optionally, try inserting records one by one as a last resort
                    print("🔄 Attempting to insert records individually as fallback...")
                    print("💡 Hint: Individual inserts are slower but more resilient to specific record issues")
                    successful_inserts = 0
                    for record in batch_data:
                        try:
                            client.table("crawled_pages").insert(record).execute()
                            successful_inserts += 1
                        except Exception as individual_error:
                            print(f"⚠️  Failed to insert individual record for URL {record['url']}: {individual_error}")
                    
                    if successful_inserts > 0:
                        print(f"✅ Successfully inserted {successful_inserts}/{len(batch_data)} records individually")
                        print("💡 Hint: Consider reducing batch_size if seeing frequent batch failures")

async def search_documents(
    client: Client, 
    query: str, 
    match_count: int = 10, 
    filter_metadata: Optional[Dict[str, Any]] = None
) -> List[Dict[str, Any]]:
    """
    Search for documents in Supabase using vector similarity.
    
    Args:
        client: Supabase client
        query: Query text
        match_count: Maximum number of results to return
        filter_metadata: Optional metadata filter
        
    Returns:
        List of matching documents
    """
    # Create embedding for the query using async if available
    try:
        query_embedding = create_embedding(query)
    except Exception as e:
        logger.warning(f"Async embedding failed for query, using sync: {e}")
        query_embedding = create_embedding(query)
    
    # Execute the search using the match_crawled_pages function
    try:
        # Only include filter parameter if filter_metadata is provided and not empty
        params = {
            'query_embedding': query_embedding,
            'match_count': match_count
        }
        
        # Only add the filter if it's actually provided and not empty
        if filter_metadata:
            params['filter'] = filter_metadata  # Pass the dictionary directly, not JSON-encoded
        
        result = client.rpc('match_crawled_pages', params).execute()
        
        return result.data
    except Exception as e:
        print(f"Error searching documents: {e}")
        return []


def extract_code_blocks(markdown_content: str, min_length: int = 50, enable_filters: bool = True, 
                        enable_adaptive_filtering: bool = False, url: str = "", 
                        metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Extract code blocks from markdown content along with context.
    Enhanced version with support for multiple code block formats, false positive filtering,
    and adaptive content type classification.
    
    Args:
        markdown_content: The markdown content to extract code blocks from
        min_length: Minimum length of code blocks to extract (default: 50 characters)
        enable_filters: Whether to apply false positive filters (default: True)
        enable_adaptive_filtering: Whether to use adaptive content type filtering (default: False)
        url: URL of the source page for content classification (optional)
        metadata: Additional metadata about the page (optional)
        
    Returns:
        List of dictionaries containing code blocks and their context
    """
    code_blocks = []
    
    # C# specific patterns and keywords for better detection
    csharp_indicators = {
        'keywords': ['using', 'namespace', 'class', 'public', 'private', 'protected', 'internal', 
                    'static', 'void', 'string', 'int', 'bool', 'var', 'async', 'await', 'Task'],
        'patterns': [r'using\s+[\w\.]+;', r'namespace\s+[\w\.]+', r'public\s+class\s+\w+', 
                    r'public\s+static\s+void\s+Main', r'Console\.WriteLine', r'\.NET', r'\.net']
    }
    
    # Extract fenced code blocks (```)
    fenced_blocks = _extract_fenced_code_blocks(markdown_content, min_length, csharp_indicators)
    code_blocks.extend(fenced_blocks)
    
    # Extract indented code blocks (4+ spaces)
    indented_blocks = _extract_indented_code_blocks(markdown_content, min_length, csharp_indicators)
    code_blocks.extend(indented_blocks)
    
    # Extract HTML code blocks (<code>, <pre>)
    html_blocks = _extract_html_code_blocks(markdown_content, min_length, csharp_indicators)
    code_blocks.extend(html_blocks)
    
    # Check if entire content is a source code file
    if _is_source_code_file(markdown_content):
        source_block = _extract_source_file_content(markdown_content, csharp_indicators)
        if source_block and len(source_block['code']) >= min_length:
            code_blocks.append(source_block)
    
    # Remove duplicates and sort by position
    code_blocks = _deduplicate_and_sort_blocks(code_blocks)
    
    # Apply filtering if enabled
    if enable_filters:
        try:
            if enable_adaptive_filtering:
                # Use adaptive filtering with content type classification
                from .adaptive_false_positive_filters import AdaptiveFalsePositiveFilterSystem
                adaptive_filter = AdaptiveFalsePositiveFilterSystem()
                code_blocks = adaptive_filter.batch_filter_adaptive(
                    code_blocks, url=url, page_content=markdown_content, metadata=metadata
                )
            else:
                # Use standard false positive filtering
                from .false_positive_filters import FalsePositiveFilterSystem
                filter_system = FalsePositiveFilterSystem()
                code_blocks = filter_system.batch_filter(code_blocks)
        except ImportError:
            # Graceful fallback if filters are not available
            pass
    
    return code_blocks


def _extract_fenced_code_blocks(markdown_content: str, min_length: int, csharp_indicators: Dict) -> List[Dict[str, Any]]:
    """Extract fenced code blocks (```) from markdown content."""
    code_blocks = []
    content = markdown_content.strip()
    start_offset = 0
    
    # Skip if content starts with triple backticks
    if content.startswith('```'):
        start_offset = 3
        print("Skipping initial triple backticks")
    
    # Find all occurrences of triple backticks
    backtick_positions = []
    pos = start_offset
    while True:
        pos = markdown_content.find('```', pos)
        if pos == -1:
            break
        backtick_positions.append(pos)
        pos += 3
    
    # Process pairs of backticks
    i = 0
    while i < len(backtick_positions) - 1:
        start_pos = backtick_positions[i]
        end_pos = backtick_positions[i + 1]
        
        # Extract the content between backticks
        code_section = markdown_content[start_pos+3:end_pos]
        
        # Parse language and content
        language, code_content = _parse_language_and_content(code_section)
        
        # Enhanced C# detection
        if not language and _is_csharp_code(code_content, csharp_indicators):
            language = "csharp"
        
        # Skip if code block is too short
        if len(code_content) < min_length:
            i += 2
            continue
        
        # Extract context with adaptive size based on code length
        context_size = min(500, max(200, len(code_content) // 4))
        context_start = max(0, start_pos - context_size)
        context_before = markdown_content[context_start:start_pos].strip()
        
        context_end = min(len(markdown_content), end_pos + 3 + context_size)
        context_after = markdown_content[end_pos + 3:context_end].strip()
        
        code_blocks.append({
            'code': code_content,
            'language': language,
            'context_before': context_before,
            'context_after': context_after,
            'full_context': f"{context_before}\n\n{code_content}\n\n{context_after}",
            'block_type': 'fenced',
            'start_pos': start_pos,
            'end_pos': end_pos,
            'confidence': _calculate_code_confidence(code_content, language, csharp_indicators)
        })
        
        i += 2
    
    return code_blocks


def _extract_indented_code_blocks(markdown_content: str, min_length: int, csharp_indicators: Dict) -> List[Dict[str, Any]]:
    """Extract indented code blocks (4+ spaces) from markdown content."""
    code_blocks = []
    lines = markdown_content.split('\n')
    
    i = 0
    while i < len(lines):
        # Check if line is indented (4+ spaces)
        if len(lines[i]) >= 4 and lines[i][:4] == '    ':
            # Found start of indented block
            start_line = i
            code_lines = []
            
            # Collect all consecutive indented lines
            while i < len(lines) and (lines[i].startswith('    ') or lines[i].strip() == ''):
                if lines[i].startswith('    '):
                    code_lines.append(lines[i][4:])  # Remove 4-space indent
                else:
                    code_lines.append('')  # Preserve empty lines
                i += 1
            
            # Join and clean up the code
            code_content = '\n'.join(code_lines).strip()
            
            if len(code_content) >= min_length:
                # Detect language
                language = _detect_language_from_content(code_content, csharp_indicators)
                
                # Calculate context
                context_before = '\n'.join(lines[max(0, start_line-5):start_line]).strip()
                context_after = '\n'.join(lines[i:i+5]).strip()
                
                code_blocks.append({
                    'code': code_content,
                    'language': language,
                    'context_before': context_before,
                    'context_after': context_after,
                    'full_context': f"{context_before}\n\n{code_content}\n\n{context_after}",
                    'block_type': 'indented',
                    'start_pos': start_line,
                    'end_pos': i,
                    'confidence': _calculate_code_confidence(code_content, language, csharp_indicators)
                })
        else:
            i += 1
    
    return code_blocks


def _extract_html_code_blocks(markdown_content: str, min_length: int, csharp_indicators: Dict) -> List[Dict[str, Any]]:
    """Extract HTML code blocks (<code>, <pre>) from markdown content."""
    code_blocks = []
    
    # Pattern for <pre> tags
    pre_pattern = r'<pre(?:\s[^>]*)?>(.+?)</pre>'
    for match in re.finditer(pre_pattern, markdown_content, re.DOTALL | re.IGNORECASE):
        code_content = match.group(1).strip()
        if len(code_content) >= min_length:
            language = _detect_language_from_content(code_content, csharp_indicators)
            
            start_pos = match.start()
            end_pos = match.end()
            
            context_size = min(300, max(150, len(code_content) // 6))
            context_before = markdown_content[max(0, start_pos - context_size):start_pos].strip()
            context_after = markdown_content[end_pos:end_pos + context_size].strip()
            
            code_blocks.append({
                'code': code_content,
                'language': language,
                'context_before': context_before,
                'context_after': context_after,
                'full_context': f"{context_before}\n\n{code_content}\n\n{context_after}",
                'block_type': 'html_pre',
                'start_pos': start_pos,
                'end_pos': end_pos,
                'confidence': _calculate_code_confidence(code_content, language, csharp_indicators)
            })
    
    # Pattern for <code> tags (for longer code blocks)
    code_pattern = r'<code(?:\s[^>]*)?>(.+?)</code>'
    for match in re.finditer(code_pattern, markdown_content, re.DOTALL | re.IGNORECASE):
        code_content = match.group(1).strip()
        if len(code_content) >= min_length:
            language = _detect_language_from_content(code_content, csharp_indicators)
            
            start_pos = match.start()
            end_pos = match.end()
            
            context_size = min(300, max(150, len(code_content) // 6))
            context_before = markdown_content[max(0, start_pos - context_size):start_pos].strip()
            context_after = markdown_content[end_pos:end_pos + context_size].strip()
            
            code_blocks.append({
                'code': code_content,
                'language': language,
                'context_before': context_before,
                'context_after': context_after,
                'full_context': f"{context_before}\n\n{code_content}\n\n{context_after}",
                'block_type': 'html_code',
                'start_pos': start_pos,
                'end_pos': end_pos,
                'confidence': _calculate_code_confidence(code_content, language, csharp_indicators)
            })
    
    return code_blocks


def _is_source_code_file(content: str) -> bool:
    """Check if the entire content appears to be a source code file."""
    # Check for common source code indicators
    lines = content.split('\n')
    if len(lines) < 3:
        return False
    
    # Count lines that look like code
    code_like_lines = 0
    for line in lines[:20]:  # Check first 20 lines
        stripped = line.strip()
        if not stripped:
            continue
        
        # Check for common code patterns
        if (stripped.startswith(('using ', 'import ', 'from ', '#include', '//')) or
            re.match(r'^\s*(public|private|protected|internal|static|class|interface|namespace)', stripped) or
            re.match(r'^\s*[\w\[\]]+\s+\w+\s*[=\(]', stripped) or  # Variable declarations or function calls
            ';' in stripped or '{' in stripped or '}' in stripped):
            code_like_lines += 1
    
    return code_like_lines > len([l for l in lines[:20] if l.strip()]) * 0.3


def _extract_source_file_content(content: str, csharp_indicators: Dict) -> Dict[str, Any]:
    """Extract entire file content as a code block."""
    language = _detect_language_from_content(content, csharp_indicators)
    
    return {
        'code': content.strip(),
        'language': language,
        'context_before': '',
        'context_after': '',
        'full_context': content.strip(),
        'block_type': 'source_file',
        'start_pos': 0,
        'end_pos': len(content),
        'confidence': _calculate_code_confidence(content, language, csharp_indicators)
    }


def _parse_language_and_content(code_section: str) -> tuple[str, str]:
    """Parse language specifier and content from a code section."""
    lines = code_section.split('\n', 1)
    if len(lines) > 1:
        first_line = lines[0].strip()
        if first_line and not ' ' in first_line and len(first_line) < 20:
            # Normalize common C# language identifiers
            language = first_line.lower()
            if language in ['c#', 'cs', 'csharp', 'c-sharp']:
                language = 'csharp'
            code_content = lines[1].strip() if len(lines) > 1 else ""
        else:
            language = ""
            code_content = code_section.strip()
    else:
        language = ""
        code_content = code_section.strip()
    
    return language, code_content


def _is_csharp_code(code_content: str, csharp_indicators: Dict) -> bool:
    """Check if code content appears to be C# code."""
    # Check for C# keywords
    keyword_matches = 0
    for keyword in csharp_indicators['keywords']:
        if re.search(rf'\b{keyword}\b', code_content, re.IGNORECASE):
            keyword_matches += 1
    
    # Check for C# patterns
    pattern_matches = 0
    for pattern in csharp_indicators['patterns']:
        if re.search(pattern, code_content, re.IGNORECASE):
            pattern_matches += 1
    
    # Consider it C# if we have enough indicators
    return keyword_matches >= 2 or pattern_matches >= 1


def _detect_language_from_content(code_content: str, csharp_indicators: Dict) -> str:
    """Detect programming language from code content."""
    if _is_csharp_code(code_content, csharp_indicators):
        return 'csharp'
    
    # Check for other common languages
    if re.search(r'\bdef\s+\w+\(', code_content) or re.search(r'\bimport\s+\w+', code_content):
        return 'python'
    elif re.search(r'\bfunction\s+\w+\(', code_content) or re.search(r'\bconst\s+\w+\s*=', code_content):
        return 'javascript'
    elif re.search(r'\bpublic\s+class\s+\w+', code_content) and 'System.' in code_content:
        return 'java'
    elif re.search(r'#include\s*<', code_content) or re.search(r'\bint\s+main\s*\(', code_content):
        return 'cpp'
    
    return ''


def _calculate_code_confidence(code_content: str, language: str, csharp_indicators: Dict) -> float:
    """Calculate confidence score for code detection."""
    confidence = 0.5  # Base confidence
    
    # Boost for identified language
    if language:
        confidence += 0.3
        if language == 'csharp':
            confidence += 0.2  # Extra boost for C#
    
    # Boost for code-like patterns
    if '{' in code_content and '}' in code_content:
        confidence += 0.1
    if ';' in code_content:
        confidence += 0.1
    
    # Boost for function/method signatures
    if re.search(r'\b\w+\s+\w+\s*\([^)]*\)\s*{', code_content):
        confidence += 0.2
    
    return min(1.0, confidence)


def _deduplicate_and_sort_blocks(code_blocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Remove duplicate code blocks and sort by position."""
    seen_codes = set()
    unique_blocks = []
    
    for block in code_blocks:
        # Create a hash of the code content
        code_hash = hash(block['code'].strip())
        if code_hash not in seen_codes:
            seen_codes.add(code_hash)
            unique_blocks.append(block)
    
    # Sort by start position
    unique_blocks.sort(key=lambda x: x.get('start_pos', 0))
    
    return unique_blocks


async def generate_code_example_summary(code: str, context_before: str, context_after: str) -> str:
    """
    Generate a summary for a code example using its surrounding context.
    
    Args:
        code: The code example
        context_before: Context before the code
        context_after: Context after the code
        
    Returns:
        A summary of what the code example demonstrates
    """
    model_choice = os.getenv("MODEL_CHOICE")
    
    # Create the prompt
    prompt = f"""<context_before>
{context_before[-500:] if len(context_before) > 500 else context_before}
</context_before>

<code_example>
{code[:1500] if len(code) > 1500 else code}
</code_example>

<context_after>
{context_after[:500] if len(context_after) > 500 else context_after}
</context_after>

Based on the code example and its surrounding context, provide a concise summary (2-3 sentences) that describes what this code example demonstrates and its purpose. Focus on the practical application and key concepts illustrated.
"""
    
    try:
        # Get LLM client and configuration
        llm_client = await get_default_llm_client()
        model_config = get_default_model_config()
        code_model = model_config.code_analysis_model
        
        response = await llm_client.generate_completion(
            model=code_model.name,
            messages=[
                {"role": "system", "content": "You are a helpful assistant that provides concise code example summaries."},
                {"role": "user", "content": prompt}
            ],
            temperature=code_model.temperature,
            max_tokens=code_model.max_tokens
        )
        
        return response.content.strip()
    
    except Exception as e:
        print(f"Error generating code example summary: {e}")
        return "Code example for demonstration purposes."


async def add_code_examples_to_supabase(
    client: Client,
    urls: List[str],
    chunk_numbers: List[int],
    code_examples: List[str],
    summaries: List[str],
    metadatas: List[Dict[str, Any]],
    batch_size: int = 20
):
    """
    Add code examples to the Supabase code_examples table in batches.
    
    Args:
        client: Supabase client
        urls: List of URLs
        chunk_numbers: List of chunk numbers
        code_examples: List of code example contents
        summaries: List of code example summaries
        metadatas: List of metadata dictionaries
        batch_size: Size of each batch for insertion
    """
    if not urls:
        return
        
    # Delete existing records for these URLs
    unique_urls = list(set(urls))
    for url in unique_urls:
        try:
            client.table('code_examples').delete().eq('url', url).execute()
        except Exception as e:
            print(f"Error deleting existing code examples for {url}: {e}")
    
    # Process in batches
    total_items = len(urls)
    for i in range(0, total_items, batch_size):
        batch_end = min(i + batch_size, total_items)
        batch_texts = []
        
        # Create combined texts for embedding (code + summary)
        for j in range(i, batch_end):
            combined_text = f"{code_examples[j]}\n\nSummary: {summaries[j]}"
            batch_texts.append(combined_text)
        
        # Create embeddings for the batch using async if available
        try:
            embeddings = create_embeddings_batch(batch_texts)
        except Exception as e:
            logger.warning(f"Async embeddings failed for code examples, using sync: {e}")
            embeddings = create_embeddings_batch(batch_texts)
        
        # Check if embeddings are valid (not all zeros)
        valid_embeddings = []
        for embedding in embeddings:
            if embedding and not all(v == 0.0 for v in embedding):
                valid_embeddings.append(embedding)
            else:
                print(f"Warning: Zero or invalid embedding detected, creating new one...")
                # Try to create a single embedding as fallback
                single_embedding = create_embedding(batch_texts[len(valid_embeddings)])
                valid_embeddings.append(single_embedding)
        
        # Prepare batch data
        batch_data = []
        for j, embedding in enumerate(valid_embeddings):
            idx = i + j
            
            # Extract source_id from URL
            parsed_url = urlparse(urls[idx])
            source_id = parsed_url.netloc or parsed_url.path
            
            batch_data.append({
                'url': urls[idx],
                'chunk_number': chunk_numbers[idx],
                'content': code_examples[idx],
                'summary': summaries[idx],
                'metadata': metadatas[idx],  # Store as JSON object, not string
                'source_id': source_id,
                'embedding': embedding
            })
        
        # Insert batch into Supabase with retry logic
        max_retries = 3
        retry_delay = 1.0  # Start with 1 second delay
        
        for retry in range(max_retries):
            try:
                client.table('code_examples').insert(batch_data).execute()
                # Success - break out of retry loop
                break
            except Exception as e:
                if retry < max_retries - 1:
                    print(f"Error inserting batch into Supabase (attempt {retry + 1}/{max_retries}): {e}")
                    print(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    # Final attempt failed
                    print(f"Failed to insert batch after {max_retries} attempts: {e}")
                    # Optionally, try inserting records one by one as a last resort
                    print("Attempting to insert records individually...")
                    successful_inserts = 0
                    for record in batch_data:
                        try:
                            client.table('code_examples').insert(record).execute()
                            successful_inserts += 1
                        except Exception as individual_error:
                            print(f"Failed to insert individual record for URL {record['url']}: {individual_error}")
                    
                    if successful_inserts > 0:
                        print(f"Successfully inserted {successful_inserts}/{len(batch_data)} records individually")
        print(f"Inserted batch {i//batch_size + 1} of {(total_items + batch_size - 1)//batch_size} code examples")


def update_source_info(client: Client, source_id: str, summary: str, word_count: int):
    """
    Update or insert source information in the sources table.
    
    Args:
        client: Supabase client
        source_id: The source ID (domain)
        summary: Summary of the source
        word_count: Total word count for the source
    """
    try:
        # Try to update existing source
        result = client.table('sources').update({
            'summary': summary,
            'total_word_count': word_count,
            'updated_at': 'now()'
        }).eq('source_id', source_id).execute()
        
        # If no rows were updated, insert new source
        if not result.data:
            client.table('sources').insert({
                'source_id': source_id,
                'summary': summary,
                'total_word_count': word_count
            }).execute()
            print(f"Created new source: {source_id}")
        else:
            print(f"Updated source: {source_id}")
            
    except Exception as e:
        print(f"Error updating source {source_id}: {e}")


async def extract_source_summary(source_id: str, content: str, max_length: int = 500) -> str:
    """
    Extract a summary for a source from its content using an LLM.
    
    This function uses the OpenAI API to generate a concise summary of the source content.
    
    Args:
        source_id: The source ID (domain)
        content: The content to extract a summary from
        max_length: Maximum length of the summary
        
    Returns:
        A summary string
    """
    # Default summary if we can't extract anything meaningful
    default_summary = f"Content from {source_id}"
    
    if not content or len(content.strip()) == 0:
        return default_summary
    
    # Get the model choice from environment variables
    model_choice = os.getenv("MODEL_CHOICE")
    
    # Limit content length to avoid token limits
    truncated_content = content[:25000] if len(content) > 25000 else content
    
    # Create the prompt for generating the summary
    prompt = f"""<source_content>
{truncated_content}
</source_content>

The above content is from the documentation for '{source_id}'. Please provide a concise summary (3-5 sentences) that describes what this library/tool/framework is about. The summary should help understand what the library/tool/framework accomplishes and the purpose.
"""
    
    try:
        # Get LLM client and configuration
        llm_client = await get_default_llm_client()
        model_config = get_default_model_config()
        query_model = model_config.query_enhancement_model
        
        # Call the LLM API to generate the summary
        response = await llm_client.generate_completion(
            model=query_model.name,
            messages=[
                {"role": "system", "content": "You are a helpful assistant that provides concise library/tool/framework summaries."},
                {"role": "user", "content": prompt}
            ],
            temperature=query_model.temperature,
            max_tokens=query_model.max_tokens
        )
        
        # Extract the generated summary
        summary = response.content.strip()
        
        # Ensure the summary is not too long
        if len(summary) > max_length:
            summary = summary[:max_length] + "..."
            
        return summary
    
    except Exception as e:
        print(f"Error generating summary with LLM for {source_id}: {e}. Using default summary.")
        return default_summary


def search_code_examples(
    client: Client, 
    query: str, 
    match_count: int = 10, 
    filter_metadata: Optional[Dict[str, Any]] = None,
    source_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Search for code examples in Supabase using vector similarity.
    
    Args:
        client: Supabase client
        query: Query text
        match_count: Maximum number of results to return
        filter_metadata: Optional metadata filter
        source_id: Optional source ID to filter results
        
    Returns:
        List of matching code examples
    """
    # Create a more descriptive query for better embedding match
    # Since code examples are embedded with their summaries, we should make the query more descriptive
    enhanced_query = f"Code example for {query}\n\nSummary: Example code showing {query}"
    
    # Create embedding for the enhanced query
    query_embedding = create_embedding(enhanced_query)
    
    # Execute the search using the match_code_examples function
    try:
        # Only include filter parameter if filter_metadata is provided and not empty
        params = {
            'query_embedding': query_embedding,
            'match_count': match_count
        }
        
        # Only add the filter if it's actually provided and not empty
        if filter_metadata:
            params['filter'] = filter_metadata
            
        # Add source filter if provided
        if source_id:
            params['source_filter'] = source_id
        
        result = client.rpc('match_code_examples', params).execute()
        
        return result.data
    except Exception as e:
        print(f"Error searching code examples: {e}")
        return []


async def process_crawl_results_to_supabase(
    supabase_client: Client,
    crawl_results: List[Dict[str, Any]],
    original_url: str
) -> Dict[str, Any]:
    """
    Process crawl results from Crawl4AI Docker API and store in Supabase.
    
    This function handles the results from async crawl jobs and stores them
    in the same format as the synchronous crawl tools.
    
    Args:
        supabase_client: Supabase client instance
        crawl_results: List of crawl results from Docker API
        original_url: The original URL that was crawled
        
    Returns:
        Summary of storage operation
    """
    try:
        # Import required functions from the main module
        from ay_rag_mcp import (
            smart_chunk_markdown,
            extract_section_info,
            extract_source_summary,
            update_source_info,
            add_documents_to_supabase,
            extract_code_blocks,
            process_code_example,
            add_code_examples_to_supabase
        )
        
        # Prepare data structures
        urls = []
        chunk_numbers = []
        contents = []
        metadatas = []
        chunk_count = 0
        
        # Track sources and their content
        source_content_map = {}
        source_word_counts = {}
        
        # Process each crawl result
        for result in crawl_results:
            if not result.get('success') or not result.get('markdown'):
                continue
                
            source_url = result.get('url', original_url)
            markdown_content = result.get('markdown', '')
            
            # Extract source_id
            parsed_url = urlparse(source_url)
            source_id = parsed_url.netloc or parsed_url.path
            
            # Store content for source summary generation
            if source_id not in source_content_map:
                source_content_map[source_id] = markdown_content[:5000]
                source_word_counts[source_id] = 0
            
            # Chunk the content
            chunks = smart_chunk_markdown(markdown_content)
            
            for i, chunk in enumerate(chunks):
                urls.append(source_url)
                chunk_numbers.append(i)
                contents.append(chunk)
                
                # Extract metadata
                meta = extract_section_info(chunk)
                meta["chunk_index"] = i
                meta["url"] = source_url
                meta["source"] = source_id
                meta["crawl_type"] = "async_job"
                metadatas.append(meta)
                
                # Accumulate word count
                source_word_counts[source_id] += meta.get("word_count", 0)
                
                chunk_count += 1
        
        # Create url_to_full_document mapping
        url_to_full_document = {}
        for result in crawl_results:
            if result.get('success') and result.get('markdown'):
                url_to_full_document[result.get('url', original_url)] = result.get('markdown', '')
        
        # Update source information
        for source_id, content in source_content_map.items():
            summary = f"Content summary for {source_id}"  # Simple summary fallback
            word_count = source_word_counts.get(source_id, 0)
            update_source_info(supabase_client, source_id, summary, word_count)
        
        # Add documentation chunks to Supabase
        add_documents_to_supabase(
            supabase_client, urls, chunk_numbers, contents, 
            metadatas, url_to_full_document, batch_size=20
        )
        
        # Extract and process code examples if enabled
        code_count = 0
        if os.getenv("USE_AGENTIC_RAG", "false") == "true":
            min_code_length = int(os.getenv("MIN_CODE_LENGTH", "50"))
            
            all_code_blocks = []
            for result in crawl_results:
                if result.get('success') and result.get('markdown'):
                    code_blocks = extract_code_blocks(
                        result.get('markdown', ''), 
                        min_length=min_code_length
                    )
                    all_code_blocks.extend([
                        (block, result.get('url', original_url)) 
                        for block in code_blocks
                    ])
            
            if all_code_blocks:
                # Process code examples in parallel using async patterns
                code_urls = []
                code_chunk_numbers = []
                code_examples = []
                code_summaries = []
                code_metadatas = []
                
                # Generate simple summaries synchronously
                summaries = []
                for block, _ in all_code_blocks:
                    # Simple fallback summary
                    code_preview = block['code'][:100] + ("..." if len(block['code']) > 100 else "")
                    summary = f"Code example: {code_preview}"
                    summaries.append(summary)
                
                # Prepare code example data
                for i, ((block, url), summary) in enumerate(zip(all_code_blocks, summaries)):
                    parsed_url = urlparse(url)
                    source_id = parsed_url.netloc or parsed_url.path
                    
                    code_urls.append(url)
                    code_chunk_numbers.append(i)
                    code_examples.append(block['code'])
                    code_summaries.append(summary)
                    
                    code_meta = {
                        "chunk_index": i,
                        "url": url,
                        "source": source_id,
                        "char_count": len(block['code']),
                        "word_count": len(block['code'].split())
                    }
                    code_metadatas.append(code_meta)
                
                # Add code examples to Supabase
                add_code_examples_to_supabase(
                    supabase_client,
                    code_urls,
                    code_chunk_numbers,
                    code_examples,
                    code_summaries,
                    code_metadatas,
                    batch_size=20
                )
                
                code_count = len(code_examples)
        
        return {
            "pages_processed": len(crawl_results),
            "chunks_stored": chunk_count,
            "code_examples_stored": code_count,
            "sources_updated": len(source_content_map)
        }
        
    except Exception as e:
        print(f"Error processing crawl results: {e}")
        return {
            "error": str(e),
            "pages_processed": 0,
            "chunks_stored": 0,
            "code_examples_stored": 0
        }


# Dashboard Helper Functions for Phase 1 MCP Tools

def get_database_statistics(client: Client) -> Dict[str, Any]:
    """
    Get comprehensive database statistics for the dashboard overview.
    
    Args:
        client: Supabase client
        
    Returns:
        Dictionary with database statistics including:
        - total_documents: Total number of documents/chunks
        - total_sources: Total number of unique sources
        - total_chunks: Same as total_documents
        - code_examples: Number of code examples
        - storage_usage_bytes: Estimated storage usage
        - last_updated: Most recent update timestamp
    """
    try:
        # Get total documents from crawled_pages
        documents_result = client.table('crawled_pages').select('id', 'created_at', 'content').execute()
        total_documents = len(documents_result.data) if documents_result.data else 0
        
        # Get total sources from sources table
        sources_result = client.table('sources').select('source_id').execute()
        total_sources = len(sources_result.data) if sources_result.data else 0
        
        # Get code examples count if table exists
        code_examples_count = 0
        try:
            code_result = client.table('code_examples').select('id').execute()
            code_examples_count = len(code_result.data) if code_result.data else 0
        except Exception:
            # Table might not exist or not accessible
            pass
        
        # Calculate storage usage estimate (based on content length)
        storage_usage_bytes = 0
        if documents_result.data:
            for doc in documents_result.data:
                if doc.get('content'):
                    storage_usage_bytes += len(doc['content'].encode('utf-8'))
        
        # Get most recent update timestamp
        last_updated = None
        if documents_result.data:
            timestamps = [doc.get('created_at') for doc in documents_result.data if doc.get('created_at')]
            if timestamps:
                last_updated = max(timestamps)
        
        return {
            "total_documents": total_documents,
            "total_sources": total_sources,
            "total_chunks": total_documents,  # Chunks are same as documents in our schema
            "code_examples": code_examples_count,
            "storage_usage_bytes": storage_usage_bytes,
            "last_updated": last_updated
        }
        
    except Exception as e:
        print(f"Error getting database statistics: {e}")
        return {
            "total_documents": 0,
            "total_sources": 0,
            "total_chunks": 0,
            "code_examples": 0,
            "storage_usage_bytes": 0,
            "last_updated": None,
            "error": str(e)
        }


def get_sources_with_metadata(client: Client) -> List[Dict[str, Any]]:
    """
    Get list of all sources with enhanced metadata for the dashboard.
    
    Args:
        client: Supabase client
        
    Returns:
        List of source dictionaries with metadata including:
        - id: Source ID (domain)
        - name: Display name for the source
        - url: Representative URL for the source
        - document_count: Number of documents from this source
        - last_crawl: Most recent crawl timestamp
        - status: Active status
        - avg_document_size: Average document size in characters
        - crawl_success_rate: Success rate (estimated as 1.0 for now)
        - code_examples_count: Number of code examples from this source
    """
    try:
        # Get basic source information
        sources_result = client.table('sources').select('*').execute()
        
        if not sources_result.data:
            return []
        
        sources_with_metadata = []
        
        for source in sources_result.data:
            source_id = source.get('source_id')
            
            # Get document count for this source
            docs_result = client.table('crawled_pages').select('id', 'created_at', 'content').eq('source_id', source_id).execute()
            document_count = len(docs_result.data) if docs_result.data else 0
            
            # Calculate average document size
            avg_document_size = 0
            if docs_result.data:
                total_size = sum(len(doc.get('content', '')) for doc in docs_result.data)
                avg_document_size = total_size // document_count if document_count > 0 else 0
            
            # Get most recent crawl timestamp
            last_crawl = None
            if docs_result.data:
                timestamps = [doc.get('created_at') for doc in docs_result.data if doc.get('created_at')]
                if timestamps:
                    last_crawl = max(timestamps)
            
            # Get code examples count for this source
            code_examples_count = 0
            try:
                code_result = client.table('code_examples').select('id').eq('source_id', source_id).execute()
                code_examples_count = len(code_result.data) if code_result.data else 0
            except Exception:
                # Table might not exist or not accessible
                pass
            
            # Build representative URL
            representative_url = f"https://{source_id}" if not source_id.startswith('http') else source_id
            
            sources_with_metadata.append({
                "id": source_id,
                "name": source.get('summary', source_id).split('.')[0] if source.get('summary') else source_id,
                "url": representative_url,
                "document_count": document_count,
                "last_crawl": last_crawl,
                "status": "active" if document_count > 0 else "inactive",
                "avg_document_size": avg_document_size,
                "crawl_success_rate": 1.0,  # We could calculate this more precisely later
                "code_examples_count": code_examples_count,
                "summary": source.get('summary', ''),
                "total_word_count": source.get('total_word_count', 0)
            })
        
        # Sort by document count (most active sources first)
        sources_with_metadata.sort(key=lambda x: x['document_count'], reverse=True)
        
        return sources_with_metadata
        
    except Exception as e:
        print(f"Error getting sources with metadata: {e}")
        return []


def calculate_storage_usage(client: Client) -> Dict[str, Any]:
    """
    Calculate detailed storage usage statistics.
    
    Args:
        client: Supabase client
        
    Returns:
        Dictionary with storage breakdown
    """
    try:
        storage_stats = {
            "total_bytes": 0,
            "documents_bytes": 0,
            "code_examples_bytes": 0,
            "breakdown_by_source": {}
        }
        
        # Calculate storage for crawled_pages
        docs_result = client.table('crawled_pages').select('content', 'source_id').execute()
        if docs_result.data:
            for doc in docs_result.data:
                content_size = len(doc.get('content', '').encode('utf-8'))
                storage_stats["documents_bytes"] += content_size
                storage_stats["total_bytes"] += content_size
                
                source = doc.get('source_id', 'unknown')
                if source not in storage_stats["breakdown_by_source"]:
                    storage_stats["breakdown_by_source"][source] = 0
                storage_stats["breakdown_by_source"][source] += content_size
        
        # Calculate storage for code_examples
        try:
            code_result = client.table('code_examples').select('code', 'summary', 'source_id').execute()
            if code_result.data:
                for code in code_result.data:
                    code_size = len(code.get('code', '').encode('utf-8'))
                    summary_size = len(code.get('summary', '').encode('utf-8'))
                    total_code_size = code_size + summary_size
                    
                    storage_stats["code_examples_bytes"] += total_code_size
                    storage_stats["total_bytes"] += total_code_size
                    
                    source = code.get('source_id', 'unknown')
                    if source not in storage_stats["breakdown_by_source"]:
                        storage_stats["breakdown_by_source"][source] = 0
                    storage_stats["breakdown_by_source"][source] += total_code_size
        except Exception:
            # Code examples table might not exist
            pass
        
        return storage_stats
        
    except Exception as e:
        print(f"Error calculating storage usage: {e}")
        return {
            "total_bytes": 0,
            "documents_bytes": 0,
            "code_examples_bytes": 0,
            "breakdown_by_source": {},
            "error": str(e)
        }