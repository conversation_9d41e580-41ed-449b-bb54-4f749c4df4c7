"""
MCP server for web crawling with Crawl4AI.

This server provides tools to crawl websites using Crawl4AI, automatically detecting
the appropriate crawl method based on URL type (sitemap, txt file, or regular webpage).
"""
from mcp.server.fastmcp import FastMCP, Context
from contextlib import asynccontextmanager
from collections.abc import AsyncIterator
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse, urldefrag
from xml.etree import ElementTree
from dotenv import load_dotenv
from supabase import Client
from pathlib import Path
import requests
import asyncio
import json
import os
import re
import concurrent.futures
import time
import logging

# Optional imports based on configuration
CrossEncoder = None
if os.getenv("USE_RERANKING", "false") == "true":
    try:
        from sentence_transformers import CrossEncoder
    except ImportError:
        print("Warning: sentence_transformers not installed. Reranking features disabled.")
        CrossEncoder = None

# Global initialization state
_server_ready = asyncio.Event()
_initialization_complete = False
_startup_metrics = {
    "server_start_time": None,
    "initialization_start_time": None,
    "initialization_end_time": None,
    "component_timings": {},
    "total_startup_time": None
}

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, MemoryAdaptiveDispatcher

from utils import (
    get_supabase_client, 
    add_documents_to_supabase, 
    search_documents,
    extract_code_blocks,
    generate_code_example_summary,
    add_code_examples_to_supabase,
    update_source_info,
    extract_source_summary,
    search_code_examples
)

# Import new components
from github_client import SimpleGitHubClient, create_github_enhanced_content
from circuit_breaker import get_circuit_breaker
from query_enhancer import SimpleQueryEnhancer

# Load environment variables from the project root .env file
project_root = Path(__file__).resolve().parent.parent
dotenv_path = project_root / '.env'

# Force override of existing environment variables
load_dotenv(dotenv_path, override=True)

# Global shared resources to avoid multiple initialization
_shared_resources = None
_initialization_lock = asyncio.Lock()

@dataclass
class SharedResources:
    """Shared resources for the AY RAG MCP server."""
    crawler: AsyncWebCrawler
    supabase_client: Client
    reranking_model: Optional[Any] = None
    
@dataclass
class AYRAGContext:
    """Context for the AY RAG MCP server."""
    crawler: AsyncWebCrawler
    supabase_client: Client
    reranking_model: Optional[Any] = None

async def _initialize_shared_resources() -> SharedResources:
    """Initialize shared resources once."""
    global _shared_resources, _startup_metrics
    
    async with _initialization_lock:
        if _shared_resources is not None:
            print("Using existing shared resources")
            return _shared_resources
        
        # Record initialization start time
        _startup_metrics["initialization_start_time"] = time.time()
        print("🚀 Initializing AY RAG MCP Server - this may take a moment...")
        print("💡 Hint: Ensure you have set OPENAI_API_KEY and SUPABASE_* environment variables")
        
        # Create browser configuration
        browser_config = BrowserConfig(
            headless=True,
            verbose=False
        )
        
        print("🌐 Initializing AsyncWebCrawler...")
        print("💡 Hint: Browser will run in headless mode for better performance")
        # Initialize the crawler
        crawler_start = time.time()
        crawler = AsyncWebCrawler(config=browser_config)
        await crawler.__aenter__()
        _startup_metrics["component_timings"]["crawler"] = time.time() - crawler_start
        globals()["crawl4ai_available"] = True
        print("✅ AsyncWebCrawler initialized successfully")
        
        print("🗄️  Initializing Supabase client...")
        print("💡 Hint: Make sure your Supabase database has the required tables (crawled_pages, code_examples, sources)")
        # Initialize Supabase client
        supabase_start = time.time()
        supabase_client = get_supabase_client()
        _startup_metrics["component_timings"]["supabase"] = time.time() - supabase_start
        globals()["supabase_available"] = True
        print("✅ Supabase client initialized successfully")
        
        # Validate LLM configuration
        print("🧠 Validating LLM configuration...")
        llm_validation_start = time.time()
        try:
            from .model_config import validate_configuration
            validate_configuration()
            globals()["llm_available"] = True
            print("✅ LLM configuration validated successfully")
        except Exception as e:
            print(f"❌ LLM configuration validation failed: {e}")
            globals()["llm_available"] = False
        _startup_metrics["component_timings"]["llm_validation"] = time.time() - llm_validation_start
        
        # Legacy OpenAI check for backward compatibility
        if os.getenv("OPENAI_API_KEY"):
            globals()["openai_available"] = True
        else:
            globals()["openai_available"] = False
        
        print("🧠 Initializing reranking model...")
        # Initialize cross-encoder model for reranking if enabled
        reranking_start = time.time()
        reranking_model = None
        if os.getenv("USE_RERANKING", "false") == "true":
            print("💡 Hint: Reranking model will improve search relevance but uses more memory")
            try:
                reranking_model = CrossEncoder("cross-encoder/ms-marco-MiniLM-L-6-v2")
                print("✅ Reranking model loaded successfully")
            except Exception as e:
                print(f"❌ Failed to load reranking model: {e}")
                print("💡 Hint: Consider setting USE_RERANKING=false if you don't need reranking")
                reranking_model = None
        else:
            print("⚪ Reranking model disabled (set USE_RERANKING=true to enable)")
        _startup_metrics["component_timings"]["reranking"] = time.time() - reranking_start
        
        _shared_resources = SharedResources(
            crawler=crawler,
            supabase_client=supabase_client,
            reranking_model=reranking_model
        )
        
        # Record final timing
        _startup_metrics["initialization_end_time"] = time.time()
        _startup_metrics["total_startup_time"] = _startup_metrics["initialization_end_time"] - _startup_metrics["initialization_start_time"]
        
        print("🎉 Shared resources initialization complete!")
        print(f"⏱️ Total initialization time: {_startup_metrics['total_startup_time']:.2f}s")
        print("💡 Hint: Server is ready to receive MCP tool calls")
        return _shared_resources

@asynccontextmanager
async def ay_rag_lifespan(server: FastMCP) -> AsyncIterator[AYRAGContext]:
    """
    Manages the AY RAG client lifecycle using shared resources.
    
    Args:
        server: The FastMCP server instance
        
    Yields:
        AYRAGContext: The context containing the crawler and Supabase client
    """
    global _initialization_complete, _mcp_session_ready
    print("Starting AY RAG MCP session...")
    
    try:
        # Get or initialize shared resources
        shared = await _initialize_shared_resources()
        
        # Initialize async migration manager for performance optimizations
        try:
            # Async migration removed - using sync operations
            print("🚀 Sync operations enabled")
        except Exception as e:
            print(f"⚠️  Async optimization initialization failed: {e}")
        
        print("🔗 All components initialized, preparing MCP context")
        
        # Create the context
        context = AYRAGContext(
            crawler=shared.crawler,
            supabase_client=shared.supabase_client,
            reranking_model=shared.reranking_model
        )
        
        # Mark initialization as complete BEFORE yielding
        # This is safe now because we have the protected SSE endpoint
        _initialization_complete = True
        _server_ready.set()
        _mcp_session_ready.set()
        
        print("✅ MCP session fully initialized and ready to accept requests")
        
        # Yield the context - MCP server is now ready
        yield context
        
        # After yield: This is cleanup code when the context manager exits
        print("🔗 MCP session context ending")
        
        # Cleanup async resources
        try:
            # Async migration removed - no cleanup needed
            print("🧹 Sync cleanup completed")
            print("✅ Async resources cleaned up")
        except Exception as e:
            print(f"⚠️  Error cleaning up async resources: {e}")
        
    except Exception as e:
        print(f"❌ Error in AY RAG session: {e}")
        print("💡 Troubleshooting hints:")
        print("   • Check all environment variables are set correctly")
        print("   • Verify Supabase database connection and permissions")
        print("   • Ensure crawl4ai-setup has been run")
        print("   • Check if required Python packages are installed")
        # Reset the ready state on error
        _initialization_complete = False
        _server_ready.clear()
        raise
    finally:
        print("AY RAG MCP session ended")

# Initialize FastMCP server
mcp = FastMCP(
    "ay-rag-mcp",
    description="MCP server for RAG and web crawling with Crawl4AI",
    lifespan=ay_rag_lifespan,
    host=os.getenv("HOST", "0.0.0.0"),
    port=int(os.getenv("PORT", "8051"))
)

# Configure logging to reduce noise from expected SSE disconnections
logging.getLogger("anyio").setLevel(logging.WARNING)
logging.getLogger("uvicorn.access").addFilter(
    lambda record: "BrokenResourceError" not in str(record.getMessage())
)

# Add health check endpoints using FastMCP custom routes
from starlette.requests import Request
from starlette.responses import JSONResponse, PlainTextResponse

logger = logging.getLogger(__name__)

# Global initialization state tracking
_initialization_complete = False

# Add health check endpoints
@mcp.custom_route("/ping", methods=["GET"])
async def ping_endpoint(request: Request) -> PlainTextResponse:
    """Simple ping endpoint for basic connectivity testing."""
    return PlainTextResponse("pong")

@mcp.custom_route("/health", methods=["GET"])
async def health_endpoint(request: Request) -> JSONResponse:
    """Comprehensive health check endpoint with diagnostic information."""
    current_time = time.time()
    
    # Basic health status
    health_data = {
        "status": "healthy" if _initialization_complete else "initializing",
        "timestamp": current_time,
        "initialization_complete": _initialization_complete,
        "components": {
            "mcp_server": "running",
            "crawl4ai": "available" if globals().get("crawl4ai_available", False) else "unknown",
            "supabase": "available" if globals().get("supabase_available", False) else "unknown",
            "llm": "available" if globals().get("llm_available", False) else "unavailable",
            "openai": "available" if globals().get("openai_available", False) else "unknown",
            "async_embeddings": "enabled" if os.getenv("ASYNC_EMBEDDINGS_ENABLED", "true") == "true" else "disabled",
            "async_database": "enabled" if os.getenv("ASYNC_DB_ENABLED", "true") == "true" else "disabled"
        }
    }
    
    # Add startup metrics if available
    if _startup_metrics.get("server_start_time"):
        health_data["startup_metrics"] = {
            "server_start_time": _startup_metrics["server_start_time"],
            "total_startup_time": _startup_metrics.get("total_startup_time"),
            "initialization_duration": _startup_metrics.get("initialization_end_time", current_time) - _startup_metrics.get("initialization_start_time", current_time)
        }
    
    status_code = 200 if _initialization_complete else 503
    return JSONResponse(health_data, status_code=status_code)

@mcp.custom_route("/ready", methods=["GET"])
async def ready_endpoint(request: Request) -> PlainTextResponse:
    """Readiness probe endpoint for container orchestration."""
    if _initialization_complete:
        return PlainTextResponse("ready", status_code=200)
    else:
        return PlainTextResponse("initializing", status_code=503, headers={"Retry-After": "5"})

@mcp.custom_route("/api/status", methods=["GET"])
async def api_status_endpoint(request: Request) -> JSONResponse:
    """API status endpoint with service status information."""
    current_time = time.time()
    
    # API status response format
    status_data = {
        "service": "ay-rag-mcp",
        "version": "0.1.0",
        "status": "healthy" if _initialization_complete else "initializing",
        "timestamp": current_time,
        "uptime": current_time - _startup_metrics.get("server_start_time", current_time) if _startup_metrics.get("server_start_time") else 0,
        "components": {
            "mcp_server": {"status": "running", "healthy": True},
            "crawl4ai": {"status": "available", "healthy": globals().get("crawl4ai_available", False)},
            "supabase": {"status": "available", "healthy": globals().get("supabase_available", False)},
            "llm": {"status": "available", "healthy": globals().get("llm_available", False)},
            "openai": {"status": "available", "healthy": globals().get("openai_available", False)}
        },
        "endpoints": {
            "health": "/health",
            "ready": "/ready", 
            "ping": "/ping",
            "status": "/api/status",
            "sse": "/sse"
        }
    }
    
    # Overall health determination
    all_healthy = all(comp["healthy"] for comp in status_data["components"].values())
    if not all_healthy:
        status_data["status"] = "degraded"
    
    status_code = 200 if _initialization_complete else 503
    return JSONResponse(status_data, status_code=status_code)

@mcp.custom_route("/metrics", methods=["GET"])
async def metrics_endpoint(request: Request) -> JSONResponse:
    """Performance metrics endpoint for async optimizations."""
    try:
        # Async performance metrics removed
        # from async_utils import get_performance_metrics
        async_metrics = {"status": "async_disabled"}
        
        # Async migration removed
        # from async_migration import get_migration_manager
        # manager = await get_migration_manager()
        migration_status = {"status": "sync_mode"}
        
        metrics_data = {
            "timestamp": time.time(),
            "async_performance": async_metrics,
            "migration_status": migration_status,
            "server_info": {
                "initialization_complete": _initialization_complete,
                "startup_metrics": _startup_metrics
            }
        }
        
        return JSONResponse(metrics_data, status_code=200)
        
    except Exception as e:
        return JSONResponse({
            "error": f"Failed to retrieve metrics: {e}",
            "timestamp": time.time()
        }, status_code=500)

# Note: FastMCP handles SSE initialization internally with proper timing
# The lifespan context manager ensures tools are only available after initialization

# Improved race condition protection - focus on proper initialization timing
_mcp_session_ready = asyncio.Event()

async def _wait_for_mcp_session_ready(timeout: float = 30.0) -> bool:
    """Wait for MCP session to be fully ready."""
    try:
        await asyncio.wait_for(_mcp_session_ready.wait(), timeout=timeout)
        return True
    except asyncio.TimeoutError:
        return False

# Import Docker tools for intelligent routing (not directly exposed)
from docker_mcp_tools import crawl_with_timeout as docker_crawl_with_timeout


def should_use_docker_crawl(url: str, max_depth: int = 3, max_concurrent: int = 10) -> bool:
    """
    Intelligent heuristic to determine if Docker crawling should be used for timeout protection.
    
    Args:
        url: The URL to crawl
        max_depth: Maximum recursion depth
        max_concurrent: Maximum concurrent sessions
        
    Returns:
        True if Docker crawling should be used, False for direct crawling
    """
    # Always use Docker for sitemaps (potentially many URLs)
    if is_sitemap(url):
        return True
    
    # Use Docker for deep crawls (likely to have many pages)
    if max_depth > 2:
        return True
        
    # Use Docker for high concurrency (resource intensive)
    if max_concurrent > 15:
        return True
        
    # Check for known large sites by domain patterns
    parsed_url = urlparse(url)
    domain = parsed_url.netloc.lower()
    
    # Known large documentation sites
    large_site_patterns = [
        'docs.', 'documentation', 'wiki', 'developer', 'api.', 'help.',
        'support.', 'kb.', 'learn.', 'tutorial', 'guide'
    ]
    
    if any(pattern in domain for pattern in large_site_patterns):
        return True
        
    # Check for specific large domains
    large_domains = [
        'github.com', 'stackoverflow.com', 'medium.com', 'dev.to',
        'readthedocs.io', 'gitbook.io', 'notion.so'
    ]
    
    if any(large_domain in domain for large_domain in large_domains):
        return True
    
    # Default to direct crawling for small sites
    return False

def rerank_results(model: Any, query: str, results: List[Dict[str, Any]], content_key: str = "content") -> List[Dict[str, Any]]:
    """
    Rerank search results using a cross-encoder model.
    
    Args:
        model: The cross-encoder model to use for reranking
        query: The search query
        results: List of search results
        content_key: The key in each result dict that contains the text content
        
    Returns:
        Reranked list of results
    """
    if not model or not results:
        return results
    
    try:
        # Extract content from results
        texts = [result.get(content_key, "") for result in results]
        
        # Create pairs of [query, document] for the cross-encoder
        pairs = [[query, text] for text in texts]
        
        # Get relevance scores from the cross-encoder
        scores = model.predict(pairs)
        
        # Add scores to results and sort by score (descending)
        for i, result in enumerate(results):
            result["rerank_score"] = float(scores[i])
        
        # Sort by rerank score
        reranked = sorted(results, key=lambda x: x.get("rerank_score", 0), reverse=True)
        
        return reranked
    except Exception as e:
        print(f"Error during reranking: {e}")
        return results

def is_sitemap(url: str) -> bool:
    """
    Check if a URL is a sitemap.
    
    Args:
        url: URL to check
        
    Returns:
        True if the URL is a sitemap, False otherwise
    """
    return url.endswith('sitemap.xml') or 'sitemap' in urlparse(url).path

def is_txt(url: str) -> bool:
    """
    Check if a URL is a text file.
    
    Args:
        url: URL to check
        
    Returns:
        True if the URL is a text file, False otherwise
    """
    return url.endswith('.txt')

def parse_sitemap(sitemap_url: str) -> List[str]:
    """
    Parse a sitemap and extract URLs.
    
    Args:
        sitemap_url: URL of the sitemap
        
    Returns:
        List of URLs found in the sitemap
    """
    resp = requests.get(sitemap_url)
    urls = []

    if resp.status_code == 200:
        try:
            tree = ElementTree.fromstring(resp.content)
            urls = [loc.text for loc in tree.findall('.//{*}loc')]
        except Exception as e:
            print(f"Error parsing sitemap XML: {e}")

    return urls

def smart_chunk_markdown(text: str, chunk_size: int = 5000) -> List[str]:
    """Split text into chunks, respecting code blocks and paragraphs."""
    chunks = []
    start = 0
    text_length = len(text)

    while start < text_length:
        # Calculate end position
        end = start + chunk_size

        # If we're at the end of the text, just take what's left
        if end >= text_length:
            chunks.append(text[start:].strip())
            break

        # Try to find a code block boundary first (```)
        chunk = text[start:end]
        code_block = chunk.rfind('```')
        if code_block != -1 and code_block > chunk_size * 0.3:
            end = start + code_block

        # If no code block, try to break at a paragraph
        elif '\n\n' in chunk:
            # Find the last paragraph break
            last_break = chunk.rfind('\n\n')
            if last_break > chunk_size * 0.3:  # Only break if we're past 30% of chunk_size
                end = start + last_break

        # If no paragraph break, try to break at a sentence
        elif '. ' in chunk:
            # Find the last sentence break
            last_period = chunk.rfind('. ')
            if last_period > chunk_size * 0.3:  # Only break if we're past 30% of chunk_size
                end = start + last_period + 1

        # Extract chunk and clean it up
        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)

        # Move start position for next chunk
        start = end

    return chunks

def extract_section_info(chunk: str) -> Dict[str, Any]:
    """
    Extracts headers and stats from a chunk.
    
    Args:
        chunk: Markdown chunk
        
    Returns:
        Dictionary with headers and stats
    """
    headers = re.findall(r'^(#+)\s+(.+)$', chunk, re.MULTILINE)
    header_str = '; '.join([f'{h[0]} {h[1]}' for h in headers]) if headers else ''

    return {
        "headers": header_str,
        "char_count": len(chunk),
        "word_count": len(chunk.split())
    }

async def _ensure_server_ready(timeout: float = 30.0):
    """Ensure the server is fully initialized before processing requests."""
    if not _initialization_complete:
        print("Waiting for server initialization to complete...")
        try:
            await asyncio.wait_for(_server_ready.wait(), timeout=timeout)
            print("Server initialization complete, processing request...")
        except asyncio.TimeoutError:
            print(f"❌ Server initialization timeout after {timeout}s")
            raise RuntimeError(f"Server initialization timeout after {timeout}s")

async def process_code_example_async(args):
    """
    Process a single code example to generate its summary (async version).
    
    Args:
        args: Tuple containing (code, context_before, context_after)
        
    Returns:
        The generated summary
    """
    code, context_before, context_after = args
    return generate_code_example_summary(code, context_before, context_after)

def process_code_example(args):
    """
    Process a single code example to generate its summary (sync wrapper).
    This function is designed to be used with concurrent.futures.
    
    Args:
        args: Tuple containing (code, context_before, context_after)
        
    Returns:
        The generated summary
    """
    try:
        code, context_before, context_after = args
        # Fallback for thread executor - create simple summary
        return f"Code example: {code[:100]}..." if len(code) > 100 else f"Code example: {code}"
    except Exception as e:
        print(f"Error in process_code_example: {e}")
        return "Code example summary unavailable"

@mcp.tool()
async def crawl_single_page(ctx: Context, url: str) -> str:
    """
    Crawl a single web page and store its content in Supabase.
    
    This tool is ideal for quickly retrieving content from a specific URL without following links.
    The content is stored in Supabase for later retrieval and querying.
    
    Args:
        ctx: The MCP server provided context
        url: URL of the web page to crawl
    
    Returns:
        Summary of the crawling operation and storage in Supabase
    """
    # Ensure server is fully initialized
    await _ensure_server_ready()
    
    try:
        # Check if context is available and initialized
        if not hasattr(ctx, 'request_context') or not hasattr(ctx.request_context, 'lifespan_context'):
            return json.dumps({
                "success": False,
                "url": url,
                "error": "Server is still initializing, please try again in a few seconds"
            }, indent=2)
        
        # Get the crawler from the context
        try:
            crawler = ctx.request_context.lifespan_context.crawler
            supabase_client = ctx.request_context.lifespan_context.supabase_client
        except AttributeError as e:
            from error_handler import create_safe_error_response
            response = create_safe_error_response(e, "server_initialization")
            response["url"] = url
            return json.dumps(response, indent=2)
        
        # Configure the crawl
        run_config = CrawlerRunConfig(cache_mode=CacheMode.BYPASS, stream=False)
        
        # Crawl the page
        result = await crawler.arun(url=url, config=run_config)
        
        if result.success and result.markdown:
            # Extract source_id
            parsed_url = urlparse(url)
            source_id = parsed_url.netloc or parsed_url.path
            
            # Chunk the content
            chunks = smart_chunk_markdown(result.markdown)
            
            # Prepare data for Supabase
            urls = []
            chunk_numbers = []
            contents = []
            metadatas = []
            total_word_count = 0
            
            for i, chunk in enumerate(chunks):
                urls.append(url)
                chunk_numbers.append(i)
                contents.append(chunk)
                
                # Extract metadata
                meta = extract_section_info(chunk)
                meta["chunk_index"] = i
                meta["url"] = url
                meta["source"] = source_id
                meta["crawl_time"] = str(asyncio.current_task().get_coro().__name__)
                metadatas.append(meta)
                
                # Accumulate word count
                total_word_count += meta.get("word_count", 0)
            
            # Create url_to_full_document mapping
            url_to_full_document = {url: result.markdown}
            
            # Update source information FIRST (before inserting documents)
            source_summary = f"Summary for {source_id}"  # Simple fallback summary
            update_source_info(supabase_client, source_id, source_summary, total_word_count)
            
            # Add documentation chunks to Supabase (AFTER source exists)
            add_documents_to_supabase(supabase_client, urls, chunk_numbers, contents, metadatas, url_to_full_document)
            
            # Extract and process code examples only if enabled
            extract_code_examples = os.getenv("USE_AGENTIC_RAG", "false") == "true"
            if extract_code_examples:
                # Use configurable minimum length for code detection (default: 50 chars)
                min_code_length = int(os.getenv("MIN_CODE_LENGTH", "50"))
                code_blocks = extract_code_blocks(result.markdown, min_length=min_code_length)
                if code_blocks:
                    code_urls = []
                    code_chunk_numbers = []
                    code_examples = []
                    code_summaries = []
                    code_metadatas = []
                    
                    # Process code examples with simple summaries
                    summaries = []
                    for block in code_blocks:
                        # Simple fallback summary
                        code_preview = block['code'][:100] + ("..." if len(block['code']) > 100 else "")
                        summary = f"Code example: {code_preview}"
                        summaries.append(summary)
                    
                    # Prepare code example data
                    for i, (block, summary) in enumerate(zip(code_blocks, summaries)):
                        code_urls.append(url)
                        code_chunk_numbers.append(i)
                        code_examples.append(block['code'])
                        code_summaries.append(summary)
                        
                        # Create metadata for code example
                        code_meta = {
                            "chunk_index": i,
                            "url": url,
                            "source": source_id,
                            "char_count": len(block['code']),
                            "word_count": len(block['code'].split())
                        }
                        code_metadatas.append(code_meta)
                    
                    # Add code examples to Supabase
                    add_code_examples_to_supabase(
                        supabase_client, 
                        code_urls, 
                        code_chunk_numbers, 
                        code_examples, 
                        code_summaries, 
                        code_metadatas
                    )
            
            return json.dumps({
                "success": True,
                "url": url,
                "chunks_stored": len(chunks),
                "code_examples_stored": len(code_blocks) if code_blocks else 0,
                "content_length": len(result.markdown),
                "total_word_count": total_word_count,
                "source_id": source_id,
                "links_count": {
                    "internal": len(result.links.get("internal", [])),
                    "external": len(result.links.get("external", []))
                }
            }, indent=2)
        else:
            return json.dumps({
                "success": False,
                "url": url,
                "error": result.error_message
            }, indent=2)
    except Exception as e:
        from error_handler import create_safe_error_response
        response = create_safe_error_response(e, "crawl_single_page")
        response["url"] = url
        return json.dumps(response, indent=2)

@mcp.tool()
async def smart_crawl_url(ctx: Context, url: str, max_depth: int = 3, max_concurrent: int = 10, chunk_size: int = 5000, force_direct: bool = False) -> str:
    """
    Intelligently crawl URLs with automatic timeout protection.
    
    This tool automatically chooses the optimal crawling method:
    - Uses Docker-based crawling for large sites to prevent timeouts
    - Uses direct crawling for small sites for faster response
    - Automatically detects URL types (sitemaps, text files, webpages)
    
    All crawled content is chunked and stored in Supabase for later retrieval and querying.
    
    Args:
        ctx: The MCP server provided context  
        url: URL to crawl (webpage, sitemap.xml, or .txt file)
        max_depth: Maximum recursion depth for regular URLs (default: 3)
        max_concurrent: Maximum concurrent browser sessions (default: 10)
        chunk_size: Maximum size of each content chunk in characters (default: 5000)
        force_direct: Force direct crawling even for large sites (default: False)
    
    Returns:
        JSON string with crawl summary and storage information
    """
    # Ensure server is fully initialized
    await _ensure_server_ready()
    
    try:
        # Intelligent routing: decide between Docker and direct crawling
        use_docker = not force_direct and should_use_docker_crawl(url, max_depth, max_concurrent)
        
        if use_docker:
            print(f"🐳 Using Docker crawling for enhanced timeout protection")
            print(f"💡 Reason: Large site detected or deep crawl requested")
            
            # Use Docker-based crawling with timeout protection
            return await docker_crawl_with_timeout(
                ctx, url, max_depth, max_concurrent, chunk_size, use_background=False
            )
        else:
            print(f"⚡ Using direct crawling for faster response")
            print(f"💡 Reason: Small site detected - should complete quickly")
        
        # Direct crawling implementation (existing logic)
        # Get the crawler from the context
        crawler = ctx.request_context.lifespan_context.crawler
        supabase_client = ctx.request_context.lifespan_context.supabase_client
        
        # Initialize variables to prevent scope issues
        crawl_results = None
        crawl_type = None
        
        # Check for GitHub repository first
        github_client = SimpleGitHubClient()
        if github_client.is_github_url(url):
            print(f"🐙 Detected GitHub repository: {url}")
            repo_info = github_client.extract_repo_info(url)
            
            if repo_info:
                owner, repo = repo_info
                print(f"💡 Attempting GitHub API crawl for {owner}/{repo}")
                
                try:
                    readme_content = github_client.get_repo_readme(owner, repo)
                    repo_metadata = github_client.get_repo_info(owner, repo)
                    
                    if readme_content and repo_metadata:
                        enhanced_content = create_github_enhanced_content(url, readme_content, repo_metadata)
                        crawl_results = [{'url': url, 'markdown': enhanced_content}]
                        crawl_type = "github_api"
                        print(f"✅ Successfully crawled via GitHub API")
                    else:
                        print(f"⚠️ GitHub API crawl failed, falling back to web crawling")
                        print("💡 Will attempt to crawl the GitHub pages directly instead")
                except Exception as e:
                    print(f"⚠️ GitHub API error: {e}, falling back to web crawling")
                    print("💡 Will attempt to crawl the GitHub pages directly instead")
        
        # Determine the crawl strategy if not GitHub or GitHub API failed
        if not crawl_results:
            try:
                if is_txt(url):
                    # For text files, use simple crawl
                    print(f"📄 Detected text file: {url}")
                    print("💡 Hint: Text files are processed directly without crawling internal links")
                    crawl_results = await crawl_markdown_file(crawler, url)
                    crawl_type = "text_file"
                elif is_sitemap(url):
                    # For sitemaps, extract URLs and crawl in parallel
                    print(f"🗺️  Detected sitemap: {url}")
                    print("💡 Hint: Extracting all URLs from sitemap for parallel crawling")
                    sitemap_urls = parse_sitemap(url)
                    if not sitemap_urls:
                        print("❌ No URLs found in sitemap, trying as regular webpage")
                        print("💡 Fallback: Treating as regular webpage for crawling")
                        crawl_results = await crawl_recursive_internal_links(crawler, [url], max_depth=1, max_concurrent=1)
                        crawl_type = "webpage_fallback"
                    else:
                        print(f"📋 Found {len(sitemap_urls)} URLs in sitemap")
                        print("💡 Hint: Large sitemaps may take significant time. Consider testing with a smaller subset first.")
                        crawl_results = await crawl_batch(crawler, sitemap_urls, max_concurrent=max_concurrent)
                        crawl_type = "sitemap"
                else:
                    # For regular URLs, use recursive crawl
                    print(f"🕷️  Starting recursive crawl of {url} (max_depth={max_depth}, max_concurrent={max_concurrent})")
                    print("💡 Hint: This may take several minutes for large sites. Consider reducing max_depth for faster results.")
                    crawl_results = await crawl_recursive_internal_links(crawler, [url], max_depth=max_depth, max_concurrent=max_concurrent)
                    crawl_type = "webpage"
            except Exception as crawl_error:
                print(f"⚠️ Primary crawl method failed: {crawl_error}")
                print("💡 Trying fallback: single page crawl")
                try:
                    # Fallback to single page crawl
                    run_config = CrawlerRunConfig(cache_mode=CacheMode.BYPASS, stream=False)
                    result = await crawler.arun(url=url, config=run_config)
                    if result.markdown:
                        crawl_results = [{'url': url, 'markdown': result.markdown}]
                        crawl_type = "single_page_fallback"
                        print("✅ Fallback single page crawl succeeded")
                    else:
                        print("❌ Fallback crawl also failed - no content found")
                except Exception as fallback_error:
                    print(f"❌ Fallback crawl also failed: {fallback_error}")
                    print("💡 All crawling methods have been exhausted")
        
        if not crawl_results:
            print("❌ No content found during crawling")
            print("💡 Troubleshooting hints:")
            print("   • Check if the URL is accessible and returns valid content")
            print("   • Verify the URL format is correct (must include http:// or https://)")
            print("   • For GitHub URLs, ensure the repository is public")
            print("   • Try a simpler URL or single page first to test connectivity")
            return json.dumps({
                "success": False,
                "url": url,
                "error": "No content found during crawling",
                "troubleshooting": [
                    "Check if URL is accessible and returns valid content",
                    "Verify URL format includes http:// or https://",
                    "For GitHub URLs, ensure repository is public",
                    "Try a simpler URL first to test connectivity"
                ]
            }, indent=2)
        
        # Process results and store in Supabase
        urls = []
        chunk_numbers = []
        contents = []
        metadatas = []
        chunk_count = 0
        
        # Track sources and their content
        source_content_map = {}
        source_word_counts = {}
        
        # Process documentation chunks
        for doc in crawl_results:
            source_url = doc['url']
            md = doc['markdown']
            chunks = smart_chunk_markdown(md, chunk_size=chunk_size)
            
            # Extract source_id
            parsed_url = urlparse(source_url)
            source_id = parsed_url.netloc or parsed_url.path
            
            # Store content for source summary generation
            if source_id not in source_content_map:
                source_content_map[source_id] = md[:5000]  # Store first 5000 chars
                source_word_counts[source_id] = 0
            
            for i, chunk in enumerate(chunks):
                urls.append(source_url)
                chunk_numbers.append(i)
                contents.append(chunk)
                
                # Extract metadata
                meta = extract_section_info(chunk)
                meta["chunk_index"] = i
                meta["url"] = source_url
                meta["source"] = source_id
                meta["crawl_type"] = crawl_type
                meta["crawl_time"] = str(asyncio.current_task().get_coro().__name__)
                metadatas.append(meta)
                
                # Accumulate word count
                source_word_counts[source_id] += meta.get("word_count", 0)
                
                chunk_count += 1
        
        # Create url_to_full_document mapping
        url_to_full_document = {}
        for doc in crawl_results:
            url_to_full_document[doc['url']] = doc['markdown']
        
        # Update source information for each unique source FIRST (before inserting documents)
        # Use async processing for source summaries
        source_summaries = []
        for source_id, content in source_content_map.items():
            try:
                summary = f"Summary for {source_id}"  # Simple fallback summary
                source_summaries.append((source_id, summary))
            except Exception as e:
                print(f"Error generating summary for {source_id}: {e}")
                source_summaries.append((source_id, f"Summary for {source_id}"))
        
        for source_id, summary in source_summaries:
            word_count = source_word_counts.get(source_id, 0)
            update_source_info(supabase_client, source_id, summary, word_count)
        
        # Add documentation chunks to Supabase (AFTER sources exist)
        batch_size = 50  # Increased for better async performance
        add_documents_to_supabase(supabase_client, urls, chunk_numbers, contents, metadatas, url_to_full_document, batch_size=batch_size)
        
        # Extract and process code examples from all documents only if enabled
        extract_code_examples_enabled = os.getenv("USE_AGENTIC_RAG", "false") == "true"
        code_examples = []  # Initialize to empty list to prevent scope issues
        
        if extract_code_examples_enabled:
            all_code_blocks = []
            code_urls = []
            code_chunk_numbers = []
            code_summaries = []
            code_metadatas = []
            
            # Extract code blocks from all documents
            for doc in crawl_results:
                source_url = doc['url']
                md = doc['markdown']
                # Use configurable minimum length for code detection (default: 50 chars)
                min_code_length = int(os.getenv("MIN_CODE_LENGTH", "50"))
                code_blocks = extract_code_blocks(md, min_length=min_code_length)
                
                if code_blocks:
                    # Process code examples with simple summaries
                    summaries = []
                    for block in code_blocks:
                        # Simple fallback summary
                        code_preview = block['code'][:100] + ("..." if len(block['code']) > 100 else "")
                        summary = f"Code example: {code_preview}"
                        summaries.append(summary)
                    
                    # Prepare code example data
                    parsed_url = urlparse(source_url)
                    source_id = parsed_url.netloc or parsed_url.path
                    
                    for i, (block, summary) in enumerate(zip(code_blocks, summaries)):
                        code_urls.append(source_url)
                        code_chunk_numbers.append(len(code_examples))  # Use global code example index
                        code_examples.append(block['code'])
                        code_summaries.append(summary)
                        
                        # Create metadata for code example
                        code_meta = {
                            "chunk_index": len(code_examples) - 1,
                            "url": source_url,
                            "source": source_id,
                            "char_count": len(block['code']),
                            "word_count": len(block['code'].split())
                        }
                        code_metadatas.append(code_meta)
            
            # Add all code examples to Supabase
            if code_examples:
                add_code_examples_to_supabase(
                    supabase_client, 
                    code_urls, 
                    code_chunk_numbers, 
                    code_examples, 
                    code_summaries, 
                    code_metadatas,
                    batch_size=batch_size
                )
        
        print(f"✅ Crawling completed successfully!")
        print(f"📊 Summary: {len(crawl_results)} pages → {chunk_count} chunks → {len(code_examples)} code examples")
        print("💡 Hint: Use 'get_available_sources' to see all sources, then 'perform_rag_query' to search content")
        
        return json.dumps({
            "success": True,
            "url": url,
            "crawl_type": crawl_type,
            "pages_crawled": len(crawl_results),
            "chunks_stored": chunk_count,
            "code_examples_stored": len(code_examples),
            "sources_updated": len(source_content_map),
            "urls_crawled": [doc['url'] for doc in crawl_results][:5] + (["..."] if len(crawl_results) > 5 else [])
        }, indent=2)
    except Exception as e:
        error_message = str(e)
        print(f"❌ Crawling failed for {url}: {error_message}")
        print("💡 Troubleshooting hints:")
        print("   • Check if URL is accessible and returns valid content")
        print("   • Verify OPENAI_API_KEY is set correctly for embedding creation")
        print("   • Ensure SUPABASE_* environment variables are configured")
        print("   • Try reducing max_concurrent or chunk_size for resource constraints")
        print("   • If using GitHub URLs, ensure the repository is public")
        
        # Provide user-friendly error message based on error type
        user_friendly_error = "An unexpected error occurred during crawling"
        if "cannot access local variable" in error_message:
            user_friendly_error = "Internal crawling logic error - this has been reported and will be fixed"
        elif "network" in error_message.lower() or "connection" in error_message.lower():
            user_friendly_error = "Network connection error - check your internet connection and the URL"
        elif "openai" in error_message.lower() or "api" in error_message.lower():
            user_friendly_error = "API configuration error - check your OpenAI API key"
        elif "supabase" in error_message.lower() or "database" in error_message.lower():
            user_friendly_error = "Database connection error - check your Supabase configuration"
        elif "timeout" in error_message.lower():
            user_friendly_error = "Operation timed out - try reducing max_depth or max_concurrent"
        
        return json.dumps({
            "success": False,
            "url": url,
            "error": user_friendly_error,
            "technical_details": error_message,
            "troubleshooting": [
                "Check if URL is accessible and returns valid content",
                "Verify OPENAI_API_KEY is set correctly",
                "Ensure SUPABASE_* environment variables are configured",
                "Try reducing max_concurrent or chunk_size for resource constraints",
                "If using GitHub URLs, ensure repository is public"
            ]
        }, indent=2)

@mcp.tool()
async def get_available_sources(ctx: Context) -> str:
    """
    Get all available sources from the sources table with enhanced metadata.
    
    This tool returns a list of all unique sources (domains) that have been crawled and stored
    in the database, along with comprehensive metadata including document counts, crawl dates,
    and source statistics. Essential for dashboard source management and filtering.

    Always use this tool before calling the RAG query or code example query tool
    with a specific source filter!
    
    Args:
        ctx: The MCP server provided context
    
    Returns:
        JSON string with the list of available sources and their enhanced metadata including:
        - id: Source ID (domain)
        - name: Display name for the source
        - url: Representative URL for the source
        - document_count: Number of documents from this source
        - last_crawl: Most recent crawl timestamp
        - status: Active status
        - avg_document_size: Average document size in characters
        - crawl_success_rate: Success rate estimation
        - code_examples_count: Number of code examples from this source
        - summary: Source description
        - total_word_count: Total word count for the source
    """
    # Ensure server is fully initialized
    await _ensure_server_ready()
    
    try:
        # Get the Supabase client from the context
        supabase_client = ctx.request_context.lifespan_context.supabase_client
        
        # Import our new helper function
        from utils import get_sources_with_metadata
        
        # Get enhanced sources with metadata
        sources = get_sources_with_metadata(supabase_client)
        
        return json.dumps({
            "success": True,
            "sources": sources,
            "count": len(sources)
        }, indent=2)
        
    except Exception as e:
        from error_handler import create_safe_error_response
        return json.dumps(create_safe_error_response(e, "get_available_sources"), indent=2)

@mcp.tool()
async def get_database_stats(ctx: Context) -> str:
    """
    Get real statistics about the Supabase database for dashboard overview.
    
    This tool provides comprehensive database statistics including total documents,
    sources, storage usage, and last update information. Essential for dashboard
    overview displays showing real data instead of mock data.
    
    Args:
        ctx: The MCP server provided context
    
    Returns:
        JSON string with database statistics including:
        - total_documents: Total number of documents/chunks stored
        - total_sources: Total number of unique sources
        - total_chunks: Same as total_documents
        - code_examples: Number of code examples (if available)
        - storage_usage_bytes: Estimated storage usage in bytes
        - last_updated: Most recent update timestamp
    """
    # Ensure server is fully initialized
    await _ensure_server_ready()
    
    try:
        # Get the Supabase client from the context
        supabase_client = ctx.request_context.lifespan_context.supabase_client
        
        # Import our new helper function
        from utils import get_database_statistics
        
        # Get comprehensive database statistics
        stats = get_database_statistics(supabase_client)
        
        return json.dumps({
            "success": True,
            "statistics": stats
        }, indent=2)
        
    except Exception as e:
        from error_handler import create_safe_error_response
        return json.dumps(create_safe_error_response(e, "get_database_stats"), indent=2)

@mcp.tool()
async def perform_rag_query(ctx: Context, query: str, source: str = None, match_count: int = 5) -> str:
    """
    Perform a RAG (Retrieval Augmented Generation) query on the stored content.
    
    This tool searches the vector database for content relevant to the query and returns
    the matching documents. Optionally filter by source domain.
    Get the source by using the get_available_sources tool before calling this search!
    
    Args:
        ctx: The MCP server provided context
        query: The search query
        source: Optional source domain to filter results (e.g., 'example.com')
        match_count: Maximum number of results to return (default: 5)
    
    Returns:
        JSON string with the search results
    """
    # Ensure server is fully initialized
    await _ensure_server_ready()
    
    try:
        # Get the Supabase client from the context
        supabase_client = ctx.request_context.lifespan_context.supabase_client
        
        # Enhance query for better search results
        query_enhancer = SimpleQueryEnhancer()
        enhanced_query_info = query_enhancer.enhance_search_query(query)
        primary_query = enhanced_query_info['primary_query']
        query_variations = enhanced_query_info['variations']
        
        # Check if hybrid search is enabled
        use_hybrid_search = os.getenv("USE_HYBRID_SEARCH", "false") == "true"
        
        # Prepare filter if source is provided and not empty
        filter_metadata = None
        if source and source.strip():
            filter_metadata = {"source": source}
        
        if use_hybrid_search:
            # Hybrid search: combine vector and keyword search
            
            # 1. Get vector search results (get more to account for filtering)
            vector_results = search_documents(
                client=supabase_client,
                query=query,
                match_count=match_count * 2,  # Get double to have room for filtering
                filter_metadata=filter_metadata
            )
            
            # 2. Get keyword search results using ILIKE
            keyword_query = supabase_client.from_('crawled_pages')\
                .select('id, url, chunk_number, content, metadata, source_id')\
                .ilike('content', f'%{query}%')
            
            # Apply source filter if provided
            if source and source.strip():
                keyword_query = keyword_query.eq('source_id', source)
            
            # Execute keyword search
            keyword_response = keyword_query.limit(match_count * 2).execute()
            keyword_results = keyword_response.data if keyword_response.data else []
            
            # 3. Combine results with preference for items appearing in both
            seen_ids = set()
            combined_results = []
            
            # First, add items that appear in both searches (these are the best matches)
            vector_ids = {r.get('id') for r in vector_results if r.get('id')}
            for kr in keyword_results:
                if kr['id'] in vector_ids and kr['id'] not in seen_ids:
                    # Find the vector result to get similarity score
                    for vr in vector_results:
                        if vr.get('id') == kr['id']:
                            # Boost similarity score for items in both results
                            vr['similarity'] = min(1.0, vr.get('similarity', 0) * 1.2)
                            combined_results.append(vr)
                            seen_ids.add(kr['id'])
                            break
            
            # Then add remaining vector results (semantic matches without exact keyword)
            for vr in vector_results:
                if vr.get('id') and vr['id'] not in seen_ids and len(combined_results) < match_count:
                    combined_results.append(vr)
                    seen_ids.add(vr['id'])
            
            # Finally, add pure keyword matches if we still need more results
            for kr in keyword_results:
                if kr['id'] not in seen_ids and len(combined_results) < match_count:
                    # Convert keyword result to match vector result format
                    combined_results.append({
                        'id': kr['id'],
                        'url': kr['url'],
                        'chunk_number': kr['chunk_number'],
                        'content': kr['content'],
                        'metadata': kr['metadata'],
                        'source_id': kr['source_id'],
                        'similarity': 0.5  # Default similarity for keyword-only matches
                    })
                    seen_ids.add(kr['id'])
            
            # Use combined results
            results = combined_results[:match_count]
            
        else:
            # Standard vector search only
            results = await search_documents(
                client=supabase_client,
                query=query,
                match_count=match_count,
                filter_metadata=filter_metadata
            )
        
        # Apply reranking if enabled
        use_reranking = os.getenv("USE_RERANKING", "false") == "true"
        if use_reranking and ctx.request_context.lifespan_context.reranking_model:
            results = rerank_results(ctx.request_context.lifespan_context.reranking_model, query, results, content_key="content")
        
        # Format the results
        formatted_results = []
        for result in results:
            formatted_result = {
                "url": result.get("url"),
                "content": result.get("content"),
                "metadata": result.get("metadata"),
                "similarity": result.get("similarity")
            }
            # Include rerank score if available
            if "rerank_score" in result:
                formatted_result["rerank_score"] = result["rerank_score"]
            formatted_results.append(formatted_result)
        
        return json.dumps({
            "success": True,
            "query": query,
            "source_filter": source,
            "search_mode": "hybrid" if use_hybrid_search else "vector",
            "reranking_applied": use_reranking and ctx.request_context.lifespan_context.reranking_model is not None,
            "results": formatted_results,
            "count": len(formatted_results)
        }, indent=2)
    except Exception as e:
        print(f"❌ RAG query failed: {str(e)}")
        print("💡 Troubleshooting hints:")
        print("   • Check if any content has been crawled using 'get_available_sources'")
        print("   • Verify source filter matches an actual source ID")
        print("   • Try a simpler query or increase match_count")
        print("   • Check Supabase database connectivity and permissions")
        from error_handler import create_safe_error_response
        response = create_safe_error_response(e, "perform_rag_query")
        response["query"] = query
        return json.dumps(response, indent=2)

@mcp.tool()
async def search_code_examples(ctx: Context, query: str, source_id: str = None, match_count: int = 5) -> str:
    """
    Search for code examples relevant to the query.
    
    This tool searches the vector database for code examples relevant to the query and returns
    the matching examples with their summaries. Optionally filter by source_id.
    Get the source_id by using the get_available_sources tool before calling this search!

    Use the get_available_sources tool first to see what sources are available for filtering.
    
    Args:
        ctx: The MCP server provided context
        query: The search query
        source_id: Optional source ID to filter results (e.g., 'example.com')
        match_count: Maximum number of results to return (default: 5)
    
    Returns:
        JSON string with the search results
    """
    # Check if code example extraction is enabled
    extract_code_examples_enabled = os.getenv("USE_AGENTIC_RAG", "false") == "true"
    if not extract_code_examples_enabled:
        return json.dumps({
            "success": False,
            "error": "Code example extraction is disabled. Perform a normal RAG search."
        }, indent=2)
    
    # Ensure server is fully initialized
    await _ensure_server_ready()
    
    try:
        # Get the Supabase client from the context
        supabase_client = ctx.request_context.lifespan_context.supabase_client
        
        # Check if hybrid search is enabled
        use_hybrid_search = os.getenv("USE_HYBRID_SEARCH", "false") == "true"
        
        # Prepare filter if source is provided and not empty
        filter_metadata = None
        if source_id and source_id.strip():
            filter_metadata = {"source": source_id}
        
        if use_hybrid_search:
            # Hybrid search: combine vector and keyword search
            
            # Import the search function from utils
            from utils import search_code_examples as search_code_examples_impl
            
            # 1. Get vector search results (get more to account for filtering)
            vector_results = search_code_examples_impl(
                client=supabase_client,
                query=query,
                match_count=match_count * 2,  # Get double to have room for filtering
                filter_metadata=filter_metadata
            )
            
            # 2. Get keyword search results using ILIKE on both content and summary
            keyword_query = supabase_client.from_('code_examples')\
                .select('id, url, chunk_number, content, summary, metadata, source_id')\
                .or_(f'content.ilike.%{query}%,summary.ilike.%{query}%')
            
            # Apply source filter if provided
            if source_id and source_id.strip():
                keyword_query = keyword_query.eq('source_id', source_id)
            
            # Execute keyword search
            keyword_response = keyword_query.limit(match_count * 2).execute()
            keyword_results = keyword_response.data if keyword_response.data else []
            
            # 3. Combine results with preference for items appearing in both
            seen_ids = set()
            combined_results = []
            
            # First, add items that appear in both searches (these are the best matches)
            vector_ids = {r.get('id') for r in vector_results if r.get('id')}
            for kr in keyword_results:
                if kr['id'] in vector_ids and kr['id'] not in seen_ids:
                    # Find the vector result to get similarity score
                    for vr in vector_results:
                        if vr.get('id') == kr['id']:
                            # Boost similarity score for items in both results
                            vr['similarity'] = min(1.0, vr.get('similarity', 0) * 1.2)
                            combined_results.append(vr)
                            seen_ids.add(kr['id'])
                            break
            
            # Then add remaining vector results (semantic matches without exact keyword)
            for vr in vector_results:
                if vr.get('id') and vr['id'] not in seen_ids and len(combined_results) < match_count:
                    combined_results.append(vr)
                    seen_ids.add(vr['id'])
            
            # Finally, add pure keyword matches if we still need more results
            for kr in keyword_results:
                if kr['id'] not in seen_ids and len(combined_results) < match_count:
                    # Convert keyword result to match vector result format
                    combined_results.append({
                        'id': kr['id'],
                        'url': kr['url'],
                        'chunk_number': kr['chunk_number'],
                        'content': kr['content'],
                        'summary': kr['summary'],
                        'metadata': kr['metadata'],
                        'source_id': kr['source_id'],
                        'similarity': 0.5  # Default similarity for keyword-only matches
                    })
                    seen_ids.add(kr['id'])
            
            # Use combined results
            results = combined_results[:match_count]
            
        else:
            # Standard vector search only
            from utils import search_code_examples as search_code_examples_impl
            
            results = search_code_examples_impl(
                client=supabase_client,
                query=query,
                match_count=match_count,
                filter_metadata=filter_metadata
            )
        
        # Apply reranking if enabled
        use_reranking = os.getenv("USE_RERANKING", "false") == "true"
        if use_reranking and ctx.request_context.lifespan_context.reranking_model:
            results = rerank_results(ctx.request_context.lifespan_context.reranking_model, query, results, content_key="content")
        
        # Format the results
        formatted_results = []
        for result in results:
            formatted_result = {
                "url": result.get("url"),
                "code": result.get("content"),
                "summary": result.get("summary"),
                "metadata": result.get("metadata"),
                "source_id": result.get("source_id"),
                "similarity": result.get("similarity")
            }
            # Include rerank score if available
            if "rerank_score" in result:
                formatted_result["rerank_score"] = result["rerank_score"]
            formatted_results.append(formatted_result)
        
        return json.dumps({
            "success": True,
            "query": query,
            "source_filter": source_id,
            "search_mode": "hybrid" if use_hybrid_search else "vector",
            "reranking_applied": use_reranking and ctx.request_context.lifespan_context.reranking_model is not None,
            "results": formatted_results,
            "count": len(formatted_results)
        }, indent=2)
    except Exception as e:
        from error_handler import create_safe_error_response
        response = create_safe_error_response(e, "search_code_examples")
        response["query"] = query
        return json.dumps(response, indent=2)


async def crawl_markdown_file(crawler: AsyncWebCrawler, url: str) -> List[Dict[str, Any]]:
    """
    Crawl a .txt or markdown file.
    
    Args:
        crawler: AsyncWebCrawler instance
        url: URL of the file
        
    Returns:
        List of dictionaries with URL and markdown content
    """
    crawl_config = CrawlerRunConfig()

    result = await crawler.arun(url=url, config=crawl_config)
    if result.success and result.markdown:
        return [{'url': url, 'markdown': result.markdown}]
    else:
        print(f"Failed to crawl {url}: {result.error_message}")
        return []

async def crawl_batch(crawler: AsyncWebCrawler, urls: List[str], max_concurrent: int = 10) -> List[Dict[str, Any]]:
    """
    Batch crawl multiple URLs in parallel.
    
    Args:
        crawler: AsyncWebCrawler instance
        urls: List of URLs to crawl
        max_concurrent: Maximum number of concurrent browser sessions
        
    Returns:
        List of dictionaries with URL and markdown content
    """
    crawl_config = CrawlerRunConfig(cache_mode=CacheMode.BYPASS, stream=False)
    dispatcher = MemoryAdaptiveDispatcher(
        memory_threshold_percent=70.0,
        check_interval=1.0,
        max_session_permit=max_concurrent
    )

    results = await crawler.arun_many(urls=urls, config=crawl_config, dispatcher=dispatcher)
    return [{'url': r.url, 'markdown': r.markdown} for r in results if r.success and r.markdown]

async def crawl_recursive_internal_links(crawler: AsyncWebCrawler, start_urls: List[str], max_depth: int = 3, max_concurrent: int = 10) -> List[Dict[str, Any]]:
    """
    Recursively crawl internal links from start URLs up to a maximum depth.
    
    Args:
        crawler: AsyncWebCrawler instance
        start_urls: List of starting URLs
        max_depth: Maximum recursion depth
        max_concurrent: Maximum number of concurrent browser sessions
        
    Returns:
        List of dictionaries with URL and markdown content
    """
    run_config = CrawlerRunConfig(cache_mode=CacheMode.BYPASS, stream=False)
    dispatcher = MemoryAdaptiveDispatcher(
        memory_threshold_percent=70.0,
        check_interval=1.0,
        max_session_permit=max_concurrent
    )

    visited = set()

    def normalize_url(url):
        return urldefrag(url)[0]

    current_urls = set([normalize_url(u) for u in start_urls])
    results_all = []

    for depth in range(max_depth):
        urls_to_crawl = [normalize_url(url) for url in current_urls if normalize_url(url) not in visited]
        if not urls_to_crawl:
            break

        results = await crawler.arun_many(urls=urls_to_crawl, config=run_config, dispatcher=dispatcher)
        next_level_urls = set()

        for result in results:
            norm_url = normalize_url(result.url)
            visited.add(norm_url)

            if result.success and result.markdown:
                results_all.append({'url': result.url, 'markdown': result.markdown})
                for link in result.links.get("internal", []):
                    next_url = normalize_url(link["href"])
                    if next_url not in visited:
                        next_level_urls.add(next_url)

        current_urls = next_level_urls

    return results_all

# TUI wrapper functions
def delete_source(domain: str) -> Dict[str, Any]:
    """
    Wrapper function for TUI to delete content by source domain.
    This is a synchronous wrapper around the async delete_content function.
    """
    try:
        async def _delete():
            context = AYRAGContext(
                crawler=None,
                supabase_client=Client(
                    os.getenv("SUPABASE_URL"),
                    os.getenv("SUPABASE_SERVICE_KEY")
                ),
                reranking_model=None
            )
            # Use the delete_content function with source parameter
            result = await delete_content(
                None,  # ctx not needed for direct call
                source=domain,
                confirm=True
            )
            return json.loads(result)
        
        # Run the async function
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(_delete())
        finally:
            loop.close()
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

async def main():
    global _startup_metrics
    _startup_metrics["server_start_time"] = time.time()
    
    transport = os.getenv("TRANSPORT", "sse")
    if transport == 'sse':
        # Run the MCP server with sse transport
        await mcp.run_sse_async()
    else:
        # Run the MCP server with stdio transport
        await mcp.run_stdio_async()

if __name__ == "__main__":
    asyncio.run(main())