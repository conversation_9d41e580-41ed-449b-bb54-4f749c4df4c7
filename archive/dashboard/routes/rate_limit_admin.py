"""
Rate limiting administration endpoints
Provides endpoints for monitoring and managing rate limits
"""

from fastapi import APIRouter, Depends, Request, HTTPException, status
from fastapi.responses import HTMLResponse, JSONResponse
from typing import Dict, List, Optional
from datetime import datetime
import logging

from ..templates_config import templates
from ..rate_limiter import rate_limiter, RateLimitRule
from ..error_handlers import log_security_event


logger = logging.getLogger(__name__)
router = APIRouter()


def admin_auth_required():
    """
    Simple admin authentication check
    In production, this should be replaced with proper authentication
    """
    # TODO: Implement proper admin authentication
    # For now, this is a placeholder that always passes
    # In production, check for admin tokens, roles, etc.
    pass


@router.get("/admin/rate-limits", response_class=HTMLResponse)
async def rate_limits_admin_page(
    request: Request,
    _auth=Depends(admin_auth_required)
):
    """Rate limits administration page"""
    try:
        # Get current rate limiting statistics
        blocked_ips = list(rate_limiter.blocked_ips.keys())
        abuse_scores = dict(rate_limiter.abuse_scores)
        rules = rate_limiter.rules
        
        context = {
            "request": request,
            "title": "Rate Limiting Administration",
            "subtitle": "Monitor and manage rate limiting",
            "blocked_ips": blocked_ips,
            "abuse_scores": abuse_scores,
            "rules": rules,
            "total_blocked": len(blocked_ips),
            "total_monitored": len(abuse_scores),
            "current_time": datetime.now()
        }
        
        return templates.TemplateResponse("pages/rate_limit_admin.html", context)
        
    except Exception as e:
        logger.error(f"Error loading rate limits admin page: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error loading administration page"
        )


@router.get("/admin/rate-limits/status/{ip}")
async def get_ip_status(
    ip: str,
    request: Request,
    _auth=Depends(admin_auth_required)
):
    """Get rate limiting status for specific IP"""
    try:
        # Validate IP format (basic validation)
        import ipaddress
        try:
            ipaddress.ip_address(ip)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid IP address format"
            )
        
        status_info = rate_limiter.get_status(ip)
        
        log_security_event(
            request=request,
            event_type="rate_limit_status_check",
            details={"checked_ip": ip},
            severity="INFO"
        )
        
        return JSONResponse(content=status_info)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting IP status for {ip}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving IP status"
        )


@router.post("/admin/rate-limits/unblock/{ip}")
async def unblock_ip(
    ip: str,
    request: Request,
    _auth=Depends(admin_auth_required)
):
    """Manually unblock an IP address"""
    try:
        # Validate IP format
        import ipaddress
        try:
            ipaddress.ip_address(ip)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid IP address format"
            )
        
        success = rate_limiter.unblock_ip(ip)
        
        if success:
            log_security_event(
                request=request,
                event_type="ip_manually_unblocked",
                details={
                    "unblocked_ip": ip,
                    "admin_ip": request.client.host if request.client else "unknown"
                },
                severity="WARNING"
            )
            
            return JSONResponse(content={
                "success": True,
                "message": f"IP {ip} has been unblocked"
            })
        else:
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "success": False,
                    "message": f"IP {ip} was not blocked"
                }
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error unblocking IP {ip}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error unblocking IP"
        )


@router.post("/admin/rate-limits/reset-abuse/{ip}")
async def reset_abuse_score(
    ip: str,
    request: Request,
    _auth=Depends(admin_auth_required)
):
    """Reset abuse score for an IP address"""
    try:
        # Validate IP format
        import ipaddress
        try:
            ipaddress.ip_address(ip)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid IP address format"
            )
        
        rate_limiter.reset_abuse_score(ip)
        
        log_security_event(
            request=request,
            event_type="abuse_score_reset",
            details={
                "target_ip": ip,
                "admin_ip": request.client.host if request.client else "unknown"
            },
            severity="INFO"
        )
        
        return JSONResponse(content={
            "success": True,
            "message": f"Abuse score reset for IP {ip}"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error resetting abuse score for IP {ip}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error resetting abuse score"
        )


@router.get("/admin/rate-limits/rules")
async def get_rate_limit_rules(
    request: Request,
    _auth=Depends(admin_auth_required)
):
    """Get current rate limiting rules"""
    try:
        rules_data = []
        for i, rule in enumerate(rate_limiter.rules):
            rules_data.append({
                "index": i,
                "requests": rule.requests,
                "window": rule.window,
                "endpoint": rule.endpoint,
                "method": rule.method,
                "description": rule.description
            })
        
        return JSONResponse(content={"rules": rules_data})
        
    except Exception as e:
        logger.error(f"Error getting rate limit rules: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving rate limit rules"
        )


@router.post("/admin/rate-limits/rules")
async def add_rate_limit_rule(
    rule_data: Dict,
    request: Request,
    _auth=Depends(admin_auth_required)
):
    """Add a new rate limiting rule"""
    try:
        # Validate rule data
        required_fields = ["requests", "window", "endpoint", "method", "description"]
        for field in required_fields:
            if field not in rule_data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Missing required field: {field}"
                )
        
        # Create new rule
        new_rule = RateLimitRule(
            requests=int(rule_data["requests"]),
            window=int(rule_data["window"]),
            endpoint=rule_data["endpoint"],
            method=rule_data["method"],
            description=rule_data["description"]
        )
        
        # Validate rule parameters
        if new_rule.requests <= 0 or new_rule.window <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Requests and window must be positive integers"
            )
        
        rate_limiter.add_rule(new_rule)
        
        log_security_event(
            request=request,
            event_type="rate_limit_rule_added",
            details={
                "rule": rule_data,
                "admin_ip": request.client.host if request.client else "unknown"
            },
            severity="INFO"
        )
        
        return JSONResponse(content={
            "success": True,
            "message": "Rate limit rule added successfully"
        })
        
    except HTTPException:
        raise
    except (ValueError, TypeError) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid rule data: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error adding rate limit rule: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error adding rate limit rule"
        )


@router.delete("/admin/rate-limits/rules/{description}")
async def remove_rate_limit_rule(
    description: str,
    request: Request,
    _auth=Depends(admin_auth_required)
):
    """Remove a rate limiting rule by description"""
    try:
        success = rate_limiter.remove_rule(description)
        
        if success:
            log_security_event(
                request=request,
                event_type="rate_limit_rule_removed",
                details={
                    "rule_description": description,
                    "admin_ip": request.client.host if request.client else "unknown"
                },
                severity="INFO"
            )
            
            return JSONResponse(content={
                "success": True,
                "message": f"Rate limit rule '{description}' removed successfully"
            })
        else:
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "success": False,
                    "message": f"Rate limit rule '{description}' not found"
                }
            )
            
    except Exception as e:
        logger.error(f"Error removing rate limit rule: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error removing rate limit rule"
        )


@router.get("/admin/rate-limits/stats")
async def get_rate_limit_stats(
    request: Request,
    _auth=Depends(admin_auth_required)
):
    """Get rate limiting statistics"""
    try:
        stats = {
            "total_rules": len(rate_limiter.rules),
            "blocked_ips": len(rate_limiter.blocked_ips),
            "monitored_ips": len(rate_limiter.abuse_scores),
            "top_abuse_scores": dict(
                sorted(
                    rate_limiter.abuse_scores.items(),
                    key=lambda x: x[1],
                    reverse=True
                )[:10]
            ),
            "blocked_ips_list": list(rate_limiter.blocked_ips.keys()),
            "current_timestamp": datetime.now().isoformat()
        }
        
        return JSONResponse(content=stats)
        
    except Exception as e:
        logger.error(f"Error getting rate limit statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving statistics"
        )