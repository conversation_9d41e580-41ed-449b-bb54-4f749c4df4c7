"""
HTMX partial update routes
"""
from fastapi import APIRouter, Request, HTTPException, Form
from fastapi.responses import HTMLResponse
import logging
import asyncio
import uuid
from datetime import datetime
from typing import Dict, Any, Optional

from ..mcp_client import get_mcp_client, CrawlOptions, SearchFilters, transform_crawl_result_for_ui, transform_search_result_for_ui
from ..templates_config import templates
from ..utils import validate_url, sanitize_search_query, clamp_numeric_value
from ..request_throttling import throttle_context, OperationType, request_throttler

router = APIRouter()
logger = logging.getLogger(__name__)

# In-memory storage for operations (in production, use Redis or database)
active_crawls: Dict[str, Dict[str, Any]] = {}
crawl_history: list = []
search_history: list = []
saved_queries: list = []


@router.get("/health/status", response_class=HTMLResponse)
async def htmx_health_status(request: Request):
    """HTMX health status updates for sidebar"""
    try:
        status_data = {
            "timestamp": datetime.now(),
            "server_status": "unknown",
            "connection_status": "disconnected"
        }
        
        # Get current server status
        try:
            async with await get_mcp_client() as mcp_client:
                health_status = await mcp_client.health_check()
                status_data["server_status"] = health_status.status
                status_data["connection_status"] = "connected"
                    
        except ConnectionError:
            status_data["server_status"] = "disconnected"
        except Exception as e:
            logger.error(f"Error getting health status: {e}")
            status_data["server_status"] = "error"
        
        # Simple status display for sidebar
        if status_data["server_status"] == "healthy":
            status_html = '''
            <div class="flex items-center" role="status" aria-live="polite">
                <div class="w-2 h-2 bg-green-400 rounded-full mr-2" aria-hidden="true"></div>
                <span>Server Online</span>
            </div>
            '''
        elif status_data["server_status"] == "disconnected":
            status_html = '''
            <div class="flex items-center" role="status" aria-live="polite">
                <div class="w-2 h-2 bg-red-400 rounded-full mr-2" aria-hidden="true"></div>
                <span>Server Offline</span>
            </div>
            '''
        else:
            status_html = '''
            <div class="flex items-center" role="status" aria-live="polite">
                <div class="w-2 h-2 bg-yellow-400 rounded-full mr-2 animate-pulse" aria-hidden="true"></div>
                <span>Server Starting</span>
            </div>
            '''
        
        return HTMLResponse(content=status_html, status_code=200)
        
    except Exception as e:
        logger.error(f"HTMX health status error: {e}")
        return HTMLResponse(
            content='<div class="flex items-center" role="status" aria-live="polite"><div class="w-2 h-2 bg-gray-400 rounded-full mr-2" aria-hidden="true"></div><span>Status Unknown</span></div>',
            status_code=200
        )


@router.get("/status", response_class=HTMLResponse)
async def htmx_status(request: Request):
    """HTMX status updates"""
    try:
        status_data = {
            "timestamp": datetime.now(),
            "server_status": "unknown",
            "connection_status": "disconnected",
            "active_operations": 0
        }
        
        # Get current server status
        try:
            async with await get_mcp_client() as mcp_client:
                health_status = await mcp_client.health_check()
                status_data["server_status"] = health_status.status
                status_data["connection_status"] = "connected"
                
                # Get active operations count if available
                try:
                    server_stats = await mcp_client.get_server_stats()
                    if server_stats:
                        status_data["active_operations"] = server_stats.get("active_operations", 0)
                except Exception:
                    pass
                    
        except ConnectionError:
            status_data["server_status"] = "disconnected"
        except Exception as e:
            logger.error(f"Error getting status: {e}")
            status_data["server_status"] = "error"
        
        return templates.TemplateResponse(
            "partials/status.html", 
            {"request": request, **status_data}
        )
        
    except Exception as e:
        logger.error(f"HTMX status error: {e}")
        return HTMLResponse(
            content='<div class="text-red-600">Status unavailable</div>',
            status_code=200
        )


@router.get("/metrics", response_class=HTMLResponse)
async def htmx_metrics(request: Request):
    """HTMX metrics updates"""
    try:
        metrics_data = {
            "timestamp": datetime.now(),
            "total_documents": 0,
            "total_sources": 0,
            "avg_query_time": 0.0,
            "server_available": False
        }
        
        # Get current metrics
        try:
            async with await get_mcp_client() as mcp_client:
                server_stats = await mcp_client.get_server_stats()
                if server_stats:
                    metrics_data.update({
                        "total_documents": server_stats.get("total_documents", 0),
                        "total_sources": server_stats.get("total_sources", 0),
                        "avg_query_time": server_stats.get("avg_query_time", 0.0),
                        "server_available": True
                    })
                    
        except Exception as e:
            logger.debug(f"Could not fetch metrics: {e}")
        
        return templates.TemplateResponse(
            "partials/metrics.html", 
            {"request": request, **metrics_data}
        )
        
    except Exception as e:
        logger.error(f"HTMX metrics error: {e}")
        return HTMLResponse(
            content='<div class="text-red-600">Metrics unavailable</div>',
            status_code=200
        )


@router.post("/crawl/start", response_class=HTMLResponse)
async def htmx_crawl_start(
    request: Request,
    url: str = Form(...),
    max_depth: int = Form(3),
    max_concurrent: int = Form(5),
    chunk_size: int = Form(1000),
    force_direct: bool = Form(False)
):
    """Start a new crawl operation via HTMX with intelligent throttling"""
    # Apply request throttling for expensive crawl operations
    async with throttle_context(request):
        try:
            # Generate unique task ID
            task_id = str(uuid.uuid4())
            
            # Enhanced URL validation using security validator
            from ..validation import validator, log_security_violation
            from ..error_handlers import handle_validation_error, ValidationError, log_security_event
            
            # Validate URL with comprehensive security checks
            url_result = validator.validate_url(url, allow_private=False)
            if not url_result.is_valid:
                # Log security violation
                log_security_event(
                    request=request,
                    event_type="url_validation_failed",
                    details={
                        "url": url[:100] + "..." if len(url) > 100 else url,
                        "error": url_result.error_message,
                        "severity": url_result.severity
                    },
                    severity="WARNING"
                )
                
                return await handle_validation_error(
                    request=request,
                    error=ValidationError(url_result.error_message),
                    field_name="URL"
                )
            
            # Use sanitized URL
            url = url_result.sanitized_value
            
            # Validate crawl options with enhanced security
            crawl_options_data = {
                "max_depth": max_depth,
                "max_concurrent": max_concurrent,  
                "chunk_size": chunk_size,
                "force_direct": force_direct
            }
            
            options_result = validator.validate_json_input(crawl_options_data, {
                'max_depth': {'type': int, 'required': False},
                'max_concurrent': {'type': int, 'required': False},
                'chunk_size': {'type': int, 'required': False},
                'force_direct': {'type': bool, 'required': False}
            })
            
            if not options_result.is_valid:
                return await handle_validation_error(
                    request=request,
                    error=ValidationError(options_result.error_message),
                    field_name="crawl options"
                )
            
            # Validate individual numeric ranges
            depth_result = validator.validate_integer_range(max_depth, 1, 10, "Max depth")
            if not depth_result.is_valid:
                return await handle_validation_error(
                    request=request,
                    error=ValidationError(depth_result.error_message),
                    field_name="max_depth"
                )
            max_depth = int(depth_result.sanitized_value)
            
            concurrent_result = validator.validate_integer_range(max_concurrent, 1, 20, "Max concurrent")
            if not concurrent_result.is_valid:
                return await handle_validation_error(
                    request=request,
                    error=ValidationError(concurrent_result.error_message),
                    field_name="max_concurrent"
                )
            max_concurrent = int(concurrent_result.sanitized_value)
            
            chunk_result = validator.validate_integer_range(chunk_size, 100, 10000, "Chunk size")
            if not chunk_result.is_valid:
                return await handle_validation_error(
                    request=request,
                    error=ValidationError(chunk_result.error_message),
                    field_name="chunk_size"
                )
            chunk_size = int(chunk_result.sanitized_value)
            
            options = CrawlOptions(
                max_depth=max_depth,
                max_concurrent=max_concurrent,
                chunk_size=chunk_size,
                force_direct=force_direct
            )
            
            # Test MCP client availability first
            try:
                async with await get_mcp_client() as mcp_client:
                    await mcp_client.health_check()
            except Exception as e:
                return templates.TemplateResponse(
                    "partials/crawl_error.html",
                    {
                        "request": request,
                        "error": f"MCP server unavailable: {str(e)}",
                        "url": url
                    }
                )
            
            # Store crawl info
            crawl_info = {
                "task_id": task_id,
                "url": url,
                "options": options,
                "status": "starting",
                "progress": 0,
                "start_time": datetime.now(),
                "end_time": None,
                "result": None
            }
            active_crawls[task_id] = crawl_info
            
            # Start crawl operation in background
            asyncio.create_task(perform_crawl_operation(task_id, url, options))
            
            # Return initial progress display
            return templates.TemplateResponse(
                "partials/crawl_progress.html",
                {
                    "request": request,
                    "task_id": task_id,
                    "url": url,
                    "status": "starting",
                    "progress": 0,
                    "start_time": crawl_info["start_time"].strftime("%H:%M:%S")
                }
            )
            
        except Exception as e:
            logger.error(f"Error starting crawl: {e}")
            return templates.TemplateResponse(
                "partials/crawl_error.html",
                {
                    "request": request,
                    "error": f"Failed to start crawl: {str(e)}",
                    "url": url
                }
            )


@router.get("/crawl/status/{task_id}", response_class=HTMLResponse)
async def htmx_crawl_status(request: Request, task_id: str):
    """Get crawl status updates via HTMX polling"""
    try:
        if task_id not in active_crawls:
            return HTMLResponse(
                content='<div class="text-red-600">Crawl operation not found</div>',
                status_code=404
            )
        
        crawl_info = active_crawls[task_id]
        
        # If crawl is complete, show results
        if crawl_info["status"] == "completed":
            result = crawl_info["result"]
            if result:
                ui_result = transform_crawl_result_for_ui(result)
                return templates.TemplateResponse(
                    "partials/crawl_result.html",
                    {
                        "request": request,
                        "task_id": task_id,
                        "result": ui_result,
                        "duration": (crawl_info["end_time"] - crawl_info["start_time"]).total_seconds()
                    }
                )
        
        # Return progress update
        return templates.TemplateResponse(
            "partials/crawl_progress.html",
            {
                "request": request,
                "task_id": task_id,
                "url": crawl_info["url"],
                "status": crawl_info["status"],
                "progress": crawl_info["progress"],
                "start_time": crawl_info["start_time"].strftime("%H:%M:%S")
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting crawl status: {e}")
        return HTMLResponse(
            content=f'<div class="text-red-600">Error getting status: {str(e)}</div>',
            status_code=200
        )


async def perform_crawl_operation(task_id: str, url: str, options: CrawlOptions):
    """Perform the actual crawl operation in background"""
    try:
        # Update status to in progress
        if task_id in active_crawls:
            active_crawls[task_id]["status"] = "crawling"
            active_crawls[task_id]["progress"] = 10
        
        # Perform the crawl
        async with await get_mcp_client() as mcp_client:
            result = await mcp_client.crawl_url(url, options)
            
            # Update progress during crawl (simulated)
            for progress in [30, 50, 70, 90]:
                if task_id in active_crawls:
                    active_crawls[task_id]["progress"] = progress
                await asyncio.sleep(0.5)  # Simulate progress
            
            # Store result
            if task_id in active_crawls:
                active_crawls[task_id]["status"] = "completed"
                active_crawls[task_id]["progress"] = 100
                active_crawls[task_id]["end_time"] = datetime.now()
                active_crawls[task_id]["result"] = result
                
                # Add to history
                crawl_history.insert(0, {
                    "task_id": task_id,
                    "url": url,
                    "success": result.success,
                    "chunks_stored": result.chunks_stored,
                    "timestamp": active_crawls[task_id]["end_time"].strftime("%Y-%m-%d %H:%M:%S"),
                    "duration": (active_crawls[task_id]["end_time"] - active_crawls[task_id]["start_time"]).total_seconds()
                })
                
                # Keep only last 10 history items
                if len(crawl_history) > 10:
                    crawl_history.pop()
                
                # Remove from active crawls after some time
                asyncio.create_task(cleanup_completed_crawl(task_id))
                
    except Exception as e:
        logger.error(f"Crawl operation failed: {e}")
        if task_id in active_crawls:
            active_crawls[task_id]["status"] = "failed"
            active_crawls[task_id]["progress"] = 0
            active_crawls[task_id]["end_time"] = datetime.now()
            active_crawls[task_id]["error"] = str(e)


async def cleanup_completed_crawl(task_id: str):
    """Remove completed crawl from active list after delay"""
    await asyncio.sleep(300)  # Keep for 5 minutes
    if task_id in active_crawls:
        del active_crawls[task_id]


@router.get("/crawl/history", response_class=HTMLResponse)
async def htmx_crawl_history(request: Request):
    """Get crawl history via HTMX"""
    try:
        return templates.TemplateResponse(
            "partials/crawl_history.html",
            {
                "request": request,
                "crawl_history": crawl_history
            }
        )
    except Exception as e:
        logger.error(f"Error getting crawl history: {e}")
        return HTMLResponse(
            content='<div class="text-red-600">History unavailable</div>',
            status_code=200
        )


# Search functionality endpoints

@router.post("/query/search", response_class=HTMLResponse)
async def htmx_search(
    request: Request,
    query: str = Form(..., min_length=1, max_length=500),
    max_results: int = Form(25, ge=1, le=100),
    min_score: float = Form(0.0, ge=0.0, le=1.0),
    source_filter: str = Form("", max_length=200)
):
    """Perform RAG search via HTMX"""
    try:
        # Enhanced search query validation using security validator
        from ..validation import validator
        from ..error_handlers import handle_validation_error, ValidationError, log_security_event
        
        # Validate search query with comprehensive security checks
        query_result = validator.validate_search_query(query, max_length=500)
        if not query_result.is_valid:
            # Log potential attack attempt
            log_security_event(
                request=request,
                event_type="search_query_validation_failed",
                details={
                    "query": query[:100] + "..." if len(query) > 100 else query,
                    "error": query_result.error_message
                },
                severity="WARNING"
            )
            
            return await handle_validation_error(
                request=request,
                error=ValidationError(query_result.error_message),
                field_name="search query"
            )
        
        # Use sanitized query
        query = query_result.sanitized_value
        
        # Validate source filter if provided
        if source_filter:
            source_result = validator.validate_source_name(source_filter)
            if not source_result.is_valid:
                return await handle_validation_error(
                    request=request,
                    error=ValidationError(source_result.error_message),
                    field_name="source filter"
                )
            source_filter = source_result.sanitized_value
        
        # Validate numeric parameters
        max_results_result = validator.validate_integer_range(max_results, 1, 100, "Max results")
        if not max_results_result.is_valid:
            return await handle_validation_error(
                request=request,
                error=ValidationError(max_results_result.error_message),
                field_name="max_results"
            )
        max_results = int(max_results_result.sanitized_value)
        
        # Create search filters
        filters = SearchFilters(
            max_results=max_results,
            min_score=min_score,
            sources=[source_filter] if source_filter else None
        )
        
        # Perform search
        async with await get_mcp_client() as mcp_client:
            search_result = await mcp_client.search_documents(query, filters)
            
            # Transform result for UI
            ui_result = transform_search_result_for_ui(search_result)
            
            # Add to search history
            if search_result.success:
                search_entry = {
                    "id": str(uuid.uuid4()),
                    "query": query,
                    "results_count": search_result.total_results,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M"),
                    "execution_time": search_result.execution_time
                }
                search_history.insert(0, search_entry)
                
                # Keep only last 50 searches
                if len(search_history) > 50:
                    search_history.pop()
            
            return templates.TemplateResponse(
                "partials/search_results.html",
                {
                    "request": request,
                    **ui_result
                }
            )
            
    except Exception as e:
        logger.error(f"Search operation failed: {e}")
        return templates.TemplateResponse(
            "partials/search_results.html",
            {
                "request": request,
                "success": False,
                "query": query,
                "error": f"Search failed: {str(e)}"
            }
        )


@router.post("/query/code-search", response_class=HTMLResponse)
async def htmx_code_search(
    request: Request,
    query: str = Form(...),
    language_filter: str = Form(""),
    max_results: int = Form(25)
):
    """Perform code-specific search via HTMX"""
    try:
        # Enhanced code search validation using security validator
        from ..validation import validator
        from ..error_handlers import handle_validation_error, ValidationError, log_security_event
        
        # Validate search query with comprehensive security checks
        query_result = validator.validate_search_query(query, max_length=500)
        if not query_result.is_valid:
            # Log potential attack attempt
            log_security_event(
                request=request,
                event_type="code_search_validation_failed",
                details={
                    "query": query[:100] + "..." if len(query) > 100 else query,
                    "error": query_result.error_message
                },
                severity="WARNING"
            )
            
            return await handle_validation_error(
                request=request,
                error=ValidationError(query_result.error_message),
                field_name="code search query"
            )
        
        # Use sanitized query
        query = query_result.sanitized_value
        
        # Validate language filter if provided
        if language_filter:
            # Language filter should only contain alphanumeric characters, dots, hyphens
            import re
            if not re.match(r'^[a-zA-Z0-9._+-]+$', language_filter):
                return await handle_validation_error(
                    request=request,
                    error=ValidationError("Invalid language filter format"),
                    field_name="language_filter"
                )
        
        # Validate max_results
        max_results_result = validator.validate_integer_range(max_results, 1, 100, "Max results")
        if not max_results_result.is_valid:
            return await handle_validation_error(
                request=request,
                error=ValidationError(max_results_result.error_message),
                field_name="max_results"
            )
        max_results = int(max_results_result.sanitized_value)
        
        # For code search, we would use a different MCP tool
        # For now, we'll use the regular search with code-specific formatting
        filters = SearchFilters(max_results=max_results)
        
        async with await get_mcp_client() as mcp_client:
            # Use dedicated code search method
            search_result = await mcp_client.search_code_examples(query, language_filter, max_results)
            
            # Transform result for UI with code-specific formatting
            ui_result = transform_search_result_for_ui(search_result)
            
            # Mark as code search for different display
            ui_result["is_code_search"] = True
            ui_result["language_filter"] = language_filter
            
            # Add to search history
            if search_result.success:
                search_entry = {
                    "id": str(uuid.uuid4()),
                    "query": f"[Code] {query}",
                    "results_count": search_result.total_results,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M"),
                    "execution_time": search_result.execution_time
                }
                search_history.insert(0, search_entry)
                
                if len(search_history) > 50:
                    search_history.pop()
            
            return templates.TemplateResponse(
                "partials/search_results.html",
                {
                    "request": request,
                    **ui_result
                }
            )
            
    except Exception as e:
        logger.error(f"Code search operation failed: {e}")
        return templates.TemplateResponse(
            "partials/search_results.html",
            {
                "request": request,
                "success": False,
                "query": query,
                "error": f"Code search failed: {str(e)}"
            }
        )


@router.get("/query/suggestions", response_class=HTMLResponse)
async def htmx_search_suggestions(request: Request, query: str = ""):
    """Get search suggestions via HTMX"""
    try:
        suggestions = []
        
        if query and len(query) >= 2:
            # Generate suggestions based on search history and common terms
            query_lower = query.lower()
            
            # Suggestions from search history
            history_suggestions = [
                {"text": entry["query"], "count": entry["results_count"]}
                for entry in search_history
                if query_lower in entry["query"].lower()
            ][:5]
            
            # Add common search patterns (in a real implementation, these would come from analytics)
            common_suggestions = []
            if "how" in query_lower:
                common_suggestions.append({"text": f"{query} to implement", "count": None})
                common_suggestions.append({"text": f"{query} examples", "count": None})
            elif "what" in query_lower:
                common_suggestions.append({"text": f"{query} is", "count": None})
                common_suggestions.append({"text": f"{query} means", "count": None})
            
            suggestions = history_suggestions + common_suggestions[:3]
        
        if suggestions:
            return templates.TemplateResponse(
                "partials/search_suggestions.html",
                {
                    "request": request,
                    "suggestions": suggestions[:8]  # Limit to 8 suggestions
                }
            )
        else:
            return HTMLResponse(content="", status_code=200)
            
    except Exception as e:
        logger.error(f"Error getting search suggestions: {e}")
        return HTMLResponse(content="", status_code=200)


@router.get("/query/history", response_class=HTMLResponse)
async def htmx_search_history(request: Request, all: bool = False):
    """Get search history via HTMX"""
    try:
        history_to_show = search_history if all else search_history[:10]
        
        return templates.TemplateResponse(
            "partials/search_history.html",
            {
                "request": request,
                "search_history": history_to_show
            }
        )
    except Exception as e:
        logger.error(f"Error getting search history: {e}")
        return HTMLResponse(
            content='<div class="text-red-600">History unavailable</div>',
            status_code=200
        )


@router.delete("/query/history/{history_id}", response_class=HTMLResponse)
async def htmx_delete_search_history(request: Request, history_id: str):
    """Delete search history entry via HTMX"""
    try:
        global search_history
        search_history = [entry for entry in search_history if entry["id"] != history_id]
        
        return templates.TemplateResponse(
            "partials/search_history.html",
            {
                "request": request,
                "search_history": search_history[:10]
            }
        )
    except Exception as e:
        logger.error(f"Error deleting search history: {e}")
        return HTMLResponse(
            content='<div class="text-red-600">Error deleting history</div>',
            status_code=200
        )


@router.get("/query/saved", response_class=HTMLResponse)
async def htmx_saved_queries(request: Request):
    """Get saved queries via HTMX"""
    try:
        return templates.TemplateResponse(
            "partials/saved_queries.html",
            {
                "request": request,
                "saved_queries": saved_queries
            }
        )
    except Exception as e:
        logger.error(f"Error getting saved queries: {e}")
        return HTMLResponse(
            content='<div class="text-red-600">Saved queries unavailable</div>',
            status_code=200
        )


@router.post("/query/save", response_class=HTMLResponse)
async def htmx_save_query(
    request: Request,
    query: str = Form(...),
    results_count: int = Form(0),
    name: str = Form("")
):
    """Save a query via HTMX"""
    try:
        saved_query = {
            "id": str(uuid.uuid4()),
            "query": query,
            "name": name if name else None,
            "results_count": results_count,
            "saved_date": datetime.now().strftime("%Y-%m-%d %H:%M")
        }
        
        # Check if query already exists
        existing = next((q for q in saved_queries if q["query"] == query), None)
        if not existing:
            saved_queries.insert(0, saved_query)
            
            # Keep only last 20 saved queries
            if len(saved_queries) > 20:
                saved_queries.pop()
            
            return HTMLResponse(
                content='<div class="text-green-600 text-sm">Query saved successfully!</div>',
                status_code=200
            )
        else:
            return HTMLResponse(
                content='<div class="text-yellow-600 text-sm">Query already saved</div>',
                status_code=200
            )
            
    except Exception as e:
        logger.error(f"Error saving query: {e}")
        return HTMLResponse(
            content='<div class="text-red-600 text-sm">Error saving query</div>',
            status_code=200
        )


@router.delete("/query/saved/{query_id}", response_class=HTMLResponse)
async def htmx_delete_saved_query(request: Request, query_id: str):
    """Delete saved query via HTMX"""
    try:
        global saved_queries
        saved_queries = [query for query in saved_queries if query["id"] != query_id]
        
        return templates.TemplateResponse(
            "partials/saved_queries.html",
            {
                "request": request,
                "saved_queries": saved_queries
            }
        )
    except Exception as e:
        logger.error(f"Error deleting saved query: {e}")
        return HTMLResponse(
            content='<div class="text-red-600">Error deleting query</div>',
            status_code=200
        )


@router.get("/query/stats", response_class=HTMLResponse)
async def htmx_search_stats(request: Request):
    """Get search statistics via HTMX"""
    try:
        # Calculate basic stats
        total_searches = len(search_history)
        total_saved = len(saved_queries)
        
        # Calculate average results per search
        avg_results = 0
        if search_history:
            avg_results = sum(entry.get("results_count", 0) for entry in search_history) / len(search_history)
        
        # Most common search terms (simplified)
        common_terms = {}
        for entry in search_history:
            words = entry["query"].lower().split()
            for word in words:
                if len(word) > 3:  # Only count words longer than 3 characters
                    common_terms[word] = common_terms.get(word, 0) + 1
        
        top_terms = sorted(common_terms.items(), key=lambda x: x[1], reverse=True)[:5]
        
        stats_data = {
            "total_searches": total_searches,
            "total_saved": total_saved,
            "avg_results": round(avg_results, 1),
            "top_terms": top_terms
        }
        
        return templates.TemplateResponse(
            "partials/search_stats.html",
            {
                "request": request,
                **stats_data
            }
        )
    except Exception as e:
        logger.error(f"Error getting search stats: {e}")
        return HTMLResponse(
            content='<div class="text-red-600">Stats unavailable</div>',
            status_code=200
        )


@router.get("/settings/audit-log", response_class=HTMLResponse)
async def htmx_settings_audit_log(request: Request):
    """Get configuration audit log via HTMX"""
    try:
        # Mock audit log entries since we don't have a real audit system
        audit_entries = [
            {
                "timestamp": datetime.now().isoformat(),
                "time_ago": "Just now",
                "change_type": "Configuration Updated",
                "summary": "Dashboard settings updated successfully",
                "success": True
            }
        ]
        
        return templates.TemplateResponse(
            "partials/settings_audit_log.html",
            {
                "request": request,
                "audit_entries": audit_entries
            }
        )
    except Exception as e:
        logger.error(f"Error getting audit log: {e}")
        return HTMLResponse(
            content='<div class="text-red-600">Audit log unavailable</div>',
            status_code=200
        )