"""
Main dashboard routes
"""
from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import HTMLResponse
import logging
from datetime import datetime
from typing import Dict, Any

from ..mcp_client import get_mcp_client
from ..templates_config import templates

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/dashboard", response_class=HTMLResponse)
async def dashboard_home(request: Request):
    """Main dashboard page with overview metrics"""
    try:
        # Get basic system information
        dashboard_data = {
            "title": "Dashboard",
            "current_time": datetime.now(),
            "system_status": "initializing",
            "quick_stats": {
                "total_documents": 0,
                "total_sources": 0,
                "recent_crawls": 0,
                "recent_queries": 0
            },
            "recent_activity": [],
            "system_alerts": []
        }
        
        # Try to get real data from Supabase via FastMCP client
        try:
            from ..fastmcp_client import get_fastmcp_client
            
            async with get_fastmcp_client() as fastmcp_client:
                # Get real database statistics
                try:
                    db_stats = await fastmcp_client.get_database_stats()
                    if db_stats and not db_stats.get("error"):
                        dashboard_data["system_status"] = "healthy"
                        dashboard_data["quick_stats"].update({
                            "total_documents": db_stats.get("total_documents", 0),
                            "total_sources": db_stats.get("total_sources", 0),
                            "recent_crawls": db_stats.get("total_chunks", 0),
                            "recent_queries": db_stats.get("code_examples", 0)
                        })
                        
                        # Create recent activity from database stats
                        if db_stats.get("last_updated"):
                            dashboard_data["recent_activity"] = [{
                                "type": "crawl",
                                "title": "Database Updated",
                                "description": f"Last activity: {db_stats.get('last_updated', 'Unknown')}",
                                "timestamp": db_stats.get("last_updated", ""),
                                "status": "success"
                            }]
                        
                except Exception as e:
                    logger.warning(f"Could not fetch database stats: {e}")
                    dashboard_data["system_status"] = "warning"
                    dashboard_data["system_alerts"].append({
                        "type": "warning",
                        "message": f"Database connection issues: {e}"
                    })
                
                # Get available sources for additional context
                try:
                    sources = await fastmcp_client.get_available_sources()
                    if sources:
                        dashboard_data["quick_stats"]["total_sources"] = len(sources)
                except Exception as e:
                    logger.debug(f"Could not fetch sources: {e}")
                    
        except ImportError as e:
            logger.warning(f"FastMCP client not available, falling back to HTTP client: {e}")
            # Fallback to existing HTTP client
            try:
                async with await get_mcp_client() as mcp_client:
                    # Get health status
                    health_status = await mcp_client.health_check()
                    dashboard_data["system_status"] = health_status.status
                    
                    # Try to get basic stats if available
                    try:
                        server_stats = await mcp_client.get_server_stats()
                        if server_stats:
                            dashboard_data["quick_stats"].update({
                                "total_documents": server_stats.get("total_documents", 0),
                                "total_sources": server_stats.get("total_sources", 0),
                                "recent_crawls": len(server_stats.get("activity_timeline", [])),
                                "recent_queries": server_stats.get("total_searches", 0)
                            })
                            
                            # Add recent activity from server stats
                            activity_timeline = server_stats.get("activity_timeline", [])
                            dashboard_data["recent_activity"] = activity_timeline[:5]  # Last 5 activities
                            
                    except Exception as e:
                        logger.warning(f"Could not fetch server stats: {e}")
                        # Still show system as healthy if health check passed
            except Exception as e:
                logger.error(f"HTTP client also failed: {e}")
                dashboard_data["system_status"] = "error"
                    
        except ConnectionError as e:
            logger.warning(f"MCP server not available: {e}")
            dashboard_data["system_status"] = "disconnected"
            dashboard_data["system_alerts"].append({
                "type": "warning",
                "message": "MCP server is not available. Some features may be limited."
            })
        except Exception as e:
            logger.error(f"Unexpected error getting dashboard data: {e}")
            dashboard_data["system_status"] = "error"
            dashboard_data["system_alerts"].append({
                "type": "error", 
                "message": "System error occurred. Please check logs."
            })
        
        return templates.TemplateResponse(
            "pages/dashboard.html", 
            {"request": request, **dashboard_data}
        )
        
    except Exception as e:
        logger.error(f"Dashboard error: {e}")
        raise HTTPException(status_code=500, detail="Dashboard unavailable")