"""
Web crawling interface routes
"""
from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import HTMLResponse
import logging
from typing import Dict, Any, List

from ..mcp_client import get_mcp_client
from ..templates_config import templates

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/", response_class=HTMLResponse)
async def crawl_interface(request: Request):
    """Crawling interface page"""
    try:
        # Import here to avoid circular imports
        from .htmx import active_crawls, crawl_history
        
        crawl_data = {
            "title": "Web Crawling",
            "server_available": False,
            "crawl_history": crawl_history[:5],  # Show last 5 items
            "default_options": {
                "max_depth": 3,
                "max_concurrent": 5,
                "chunk_size": 1000
            },
            "active_crawls": []
        }
        
        # Get active crawls
        for task_id, crawl_info in active_crawls.items():
            if crawl_info["status"] in ["starting", "crawling"]:
                crawl_data["active_crawls"].append({
                    "task_id": task_id,
                    "url": crawl_info["url"],
                    "progress": crawl_info["progress"],
                    "start_time": crawl_info["start_time"].strftime("%H:%M:%S")
                })
        
        # Try to get real data from Supabase via FastMCP client
        try:
            from ..fastmcp_client import get_fastmcp_client
            
            async with get_fastmcp_client() as fastmcp_client:
                # Check server availability and get real crawl history
                try:
                    db_stats = await fastmcp_client.get_database_stats()
                    if db_stats and not db_stats.get("error"):
                        crawl_data["server_available"] = True
                        
                        # Get real crawl history from database content
                        try:
                            content_list = await fastmcp_client.list_content(limit=10)
                            if content_list.get("success") and content_list.get("content"):
                                crawl_data["crawl_history"] = []
                                for item in content_list["content"][:5]:  # Last 5 items
                                    crawl_data["crawl_history"].append({
                                        "url": item.get("url", "Unknown"),
                                        "status": "completed" if item.get("chunks", 0) > 0 else "failed",
                                        "chunks_stored": item.get("chunks", 0),
                                        "timestamp": item.get("created_at", "")
                                    })
                        except Exception as e:
                            logger.debug(f"Could not fetch crawl history: {e}")
                        
                        # Set default options based on server capabilities
                        crawl_data["default_options"] = {
                            "max_depth": 3,
                            "max_concurrent": 5 if db_stats.get("total_documents", 0) < 1000 else 3,
                            "chunk_size": 5000  # Use server default
                        }
                        
                except Exception as e:
                    logger.warning(f"Could not fetch database stats: {e}")
                    crawl_data["server_available"] = False
                    
        except ImportError as e:
            logger.warning(f"FastMCP client not available, falling back to HTTP client: {e}")
            # Fallback to existing HTTP client
            try:
                async with await get_mcp_client() as mcp_client:
                    health_status = await mcp_client.health_check()
                    crawl_data["server_available"] = health_status.status == "healthy"
                        
            except ConnectionError as e:
                logger.warning(f"HTTP client also failed: {e}")
                crawl_data["server_available"] = False
                    
        except ConnectionError as e:
            logger.warning(f"MCP server not available for crawling: {e}")
            crawl_data["server_available"] = False
        except Exception as e:
            logger.error(f"Error checking crawl interface: {e}")
            crawl_data["server_available"] = False
        
        return templates.TemplateResponse(
            "pages/crawl.html", 
            {"request": request, **crawl_data}
        )
        
    except Exception as e:
        logger.error(f"Crawl interface error: {e}")
        raise HTTPException(status_code=500, detail="Crawl interface unavailable")