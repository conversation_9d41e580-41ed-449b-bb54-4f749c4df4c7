"""
Content management routes - Simple CRUD for crawled sources
"""
from fastapi import APIRouter, Request, HTTPException, Form
from fastapi.responses import HTMLResponse
import logging
from typing import Dict, Any, List

from ..mcp_client import get_mcp_client
from ..templates_config import templates

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/", response_class=HTMLResponse)
async def content_management(request: Request):
    """Content management page"""
    try:
        content_data = {
            "title": "Content Management",
            "server_available": False,
            "sources": [],
            "total_documents": 0,
            "total_sources": 0
        }
        
        # Try to get real data from Supabase via FastMCP client
        try:
            from ..fastmcp_client import get_fastmcp_client
            
            async with get_fastmcp_client() as fastmcp_client:
                # Get database stats for overview
                try:
                    db_stats = await fastmcp_client.get_database_stats()
                    if db_stats and not db_stats.get("error"):
                        content_data["server_available"] = True
                        content_data["total_documents"] = db_stats.get("total_documents", 0)
                        content_data["total_sources"] = db_stats.get("total_sources", 0)
                        
                        # Get real sources data
                        try:
                            sources = await fastmcp_client.get_available_sources()
                            if sources:
                                content_data["sources"] = []
                                for source in sources:
                                    content_data["sources"].append({
                                        "name": source.get("name", source.get("id", "Unknown")),
                                        "count": source.get("document_count", 0),
                                        "size": source.get("total_word_count", 0),
                                        "last_crawl": source.get("last_crawl", ""),
                                        "url": source.get("url", ""),
                                        "status": "active",
                                        "summary": source.get("summary", "")
                                    })
                        except Exception as e:
                            logger.debug(f"Could not fetch sources: {e}")
                            
                    else:
                        content_data["server_available"] = False
                        
                except Exception as e:
                    logger.warning(f"Could not fetch database stats: {e}")
                    content_data["server_available"] = False
                    
        except ImportError as e:
            logger.warning(f"FastMCP client not available, falling back to HTTP client: {e}")
            # Fallback to existing HTTP client
            try:
                async with await get_mcp_client() as mcp_client:
                    health_status = await mcp_client.health_check()
                    content_data["server_available"] = health_status.status == "healthy"
                    
                    if content_data["server_available"]:
                        # Get server stats for overview
                        server_stats = await mcp_client.get_server_stats()
                        if server_stats:
                            content_data["total_documents"] = server_stats.get("total_documents", 0)
                            content_data["total_sources"] = server_stats.get("total_sources", 0)
                            
                            # Get sources from content breakdown
                            if "content_breakdown" in server_stats and "by_source" in server_stats["content_breakdown"]:
                                content_data["sources"] = server_stats["content_breakdown"]["by_source"]
                        
            except Exception as e:
                logger.warning(f"HTTP client also failed: {e}")
                content_data["server_available"] = False
                    
        except Exception as e:
            logger.error(f"Error getting content data: {e}")
            content_data["server_available"] = False
        
        return templates.TemplateResponse(
            "pages/content.html", 
            {"request": request, **content_data}
        )
        
    except Exception as e:
        logger.error(f"Content management error: {e}")
        raise HTTPException(status_code=500, detail="Content management unavailable")


@router.delete("/htmx/source/{source_name}", response_class=HTMLResponse)
async def delete_source(request: Request, source_name: str):
    """Delete a source and all its documents"""
    try:
        success = False
        message = ""
        
        # Try to delete using FastMCP client
        try:
            from ..fastmcp_client import get_fastmcp_client
            
            async with get_fastmcp_client() as fastmcp_client:
                # Delete content from the source
                result = await fastmcp_client.delete_content(source=source_name, confirm=True)
                if result.get("success"):
                    success = True
                    message = f"Source '{source_name}' deleted successfully"
                    logger.info(f"Successfully deleted source: {source_name}")
                else:
                    success = False
                    message = f"Failed to delete source '{source_name}': {result.get('error', 'Unknown error')}"
                    
        except ImportError:
            logger.warning("FastMCP client not available for deletion")
            # Fallback - simulate deletion
            success = True
            message = f"Source '{source_name}' deletion requested (simulated)"
        except Exception as e:
            logger.error(f"Error deleting source {source_name}: {e}")
            success = False
            message = f"Failed to delete source '{source_name}': {str(e)}"
        
        return templates.TemplateResponse(
            "partials/delete_result.html",
            {
                "request": request,
                "success": success,
                "message": message,
                "source_name": source_name
            }
        )
        
    except Exception as e:
        logger.error(f"Error deleting source {source_name}: {e}")
        return templates.TemplateResponse(
            "partials/delete_result.html",
            {
                "request": request,
                "success": False,
                "message": f"Failed to delete source '{source_name}': {str(e)}",
                "source_name": source_name
            }
        )


@router.post("/htmx/source/{source_name}/refresh", response_class=HTMLResponse)
async def refresh_source(request: Request, source_name: str):
    """Re-crawl a source"""
    try:
        success = False
        message = ""
        
        # Try to refresh using FastMCP client
        try:
            from ..fastmcp_client import get_fastmcp_client
            
            async with get_fastmcp_client() as fastmcp_client:
                # Get source details first to find the URL
                sources = await fastmcp_client.get_available_sources()
                source_url = None
                
                for source in sources:
                    if source.get("name") == source_name or source.get("id") == source_name:
                        source_url = source.get("url")
                        break
                
                if source_url:
                    # Trigger re-crawl of the source
                    result = await fastmcp_client.smart_crawl_url(url=source_url, max_depth=3, max_concurrent=5)
                    if result.get("success"):
                        success = True
                        message = f"Re-crawl started for '{source_name}'"
                        logger.info(f"Successfully started re-crawl for source: {source_name}")
                    else:
                        success = False
                        message = f"Failed to start re-crawl for '{source_name}': {result.get('error', 'Unknown error')}"
                else:
                    success = False
                    message = f"Could not find URL for source '{source_name}'"
                    
        except ImportError:
            logger.warning("FastMCP client not available for refresh")
            # Fallback - simulate refresh
            success = True
            message = f"Re-crawl requested for '{source_name}' (simulated)"
        except Exception as e:
            logger.error(f"Error refreshing source {source_name}: {e}")
            success = False
            message = f"Failed to refresh source '{source_name}': {str(e)}"
        
        return templates.TemplateResponse(
            "partials/refresh_result.html",
            {
                "request": request,
                "success": success,
                "message": message,
                "source_name": source_name
            }
        )
        
    except Exception as e:
        logger.error(f"Error refreshing source {source_name}: {e}")
        return templates.TemplateResponse(
            "partials/refresh_result.html",
            {
                "request": request,
                "success": False,
                "message": f"Failed to refresh source '{source_name}': {str(e)}",
                "source_name": source_name
            }
        )