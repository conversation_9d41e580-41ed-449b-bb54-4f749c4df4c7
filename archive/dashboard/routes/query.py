"""
RAG query interface routes
"""
from fastapi import API<PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import HTMLResponse
import logging
from typing import Dict, Any, List

from ..mcp_client import get_mcp_client
from ..templates_config import templates

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/", response_class=HTMLResponse)
async def query_interface(request: Request):
    """RAG query interface page"""
    try:
        query_data = {
            "title": "RAG Query",
            "server_available": False,
            "available_sources": [],
            "query_history": [],
            "search_suggestions": [],
            "rag_strategies": {
                "contextual_embeddings": False,
                "hybrid_search": False,
                "agentic_rag": False,
                "reranking": False
            }
        }
        
        # Try to get real data from Supabase via FastMCP client
        try:
            from ..fastmcp_client import get_fastmcp_client
            
            async with get_fastmcp_client() as fastmcp_client:
                # Get available sources for filtering
                try:
                    sources = await fastmcp_client.get_available_sources()
                    if sources:
                        query_data["available_sources"] = sources
                        query_data["server_available"] = True
                        
                        # Transform sources for UI
                        for source in query_data["available_sources"]:
                            if "last_crawl" in source and source["last_crawl"]:
                                source["last_crawl_formatted"] = source["last_crawl"]
                                
                except Exception as e:
                    logger.warning(f"Could not fetch sources via FastMCP: {e}")
                    query_data["server_available"] = False
                
                # Get database stats to check RAG capabilities
                try:
                    db_stats = await fastmcp_client.get_database_stats()
                    if db_stats and not db_stats.get("error"):
                        query_data["server_available"] = True
                        
                        # Set RAG strategies based on available data
                        query_data["rag_strategies"] = {
                            "contextual_embeddings": True,  # Available with embeddings
                            "hybrid_search": True,  # Always available
                            "agentic_rag": db_stats.get("code_examples", 0) > 0,  # Available if code examples exist
                            "reranking": True  # Available with vector search
                        }
                        
                        # Add search suggestions based on existing data
                        if db_stats.get("total_documents", 0) > 0:
                            query_data["search_suggestions"] = [
                                "How to implement authentication",
                                "API endpoint examples", 
                                "Database setup guide",
                                "Error handling patterns",
                                "Performance optimization"
                            ]
                            
                except Exception as e:
                    logger.warning(f"Could not fetch database stats: {e}")
                    
        except ImportError as e:
            logger.warning(f"FastMCP client not available, falling back to HTTP client: {e}")
            # Fallback to existing HTTP client
            try:
                async with await get_mcp_client() as mcp_client:
                    health_status = await mcp_client.health_check()
                    query_data["server_available"] = health_status.status == "healthy"
                    
                    if query_data["server_available"]:
                        # Get available sources for filtering
                        try:
                            sources = await mcp_client.get_sources()
                            query_data["available_sources"] = sources or []
                        except Exception as e:
                            logger.debug(f"Could not fetch sources: {e}")
                        
                        # Get RAG strategy status
                        try:
                            server_stats = await mcp_client.get_server_stats()
                            if server_stats and "rag_strategies" in server_stats:
                                query_data["rag_strategies"].update(server_stats["rag_strategies"])
                        except Exception as e:
                            logger.debug(f"Could not fetch RAG strategies: {e}")
                            
            except ConnectionError as e:
                logger.warning(f"HTTP client also failed: {e}")
                query_data["server_available"] = False
                    
        except ConnectionError as e:
            logger.warning(f"MCP server not available for queries: {e}")
            query_data["server_available"] = False
        except Exception as e:
            logger.error(f"Error checking query interface: {e}")
            query_data["server_available"] = False
        
        return templates.TemplateResponse(
            "pages/query.html", 
            {"request": request, **query_data}
        )
        
    except Exception as e:
        logger.error(f"Query interface error: {e}")
        raise HTTPException(status_code=500, detail="Query interface unavailable")