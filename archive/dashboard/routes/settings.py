"""
System settings and configuration routes
"""
from fastapi import API<PERSON>outer, Request, HTTPException
from fastapi.responses import HTMLResponse
import logging
import json
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path

from ..mcp_client import get_mcp_client
from ..config import settings as app_settings
from ..templates_config import templates

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/", response_class=HTMLResponse)
async def settings_interface(request: Request):
    """Settings interface page"""
    try:
        settings_data = {
            "title": "Settings",
            "server_available": False,
            "current_settings": {
                "rag_strategies": {
                    "contextual_embeddings": False,
                    "hybrid_search": False,
                    "agentic_rag": False,
                    "reranking": False
                },
                "crawling_parameters": {
                    "default_max_depth": 3,
                    "default_max_concurrent": 5,
                    "default_chunk_size": 1000
                },
                "code_detection": {
                    "enabled": True,
                    "min_code_length": 50,
                    "filter_options": []
                },
                "performance": {
                    "cache_enabled": True,
                    "timeout_seconds": 30,
                    "retry_attempts": 3
                }
            },
            "dashboard_settings": {
                "refresh_interval": app_settings.REFRESH_INTERVAL,
                "max_search_results": app_settings.MAX_SEARCH_RESULTS,
                "enable_real_time": app_settings.ENABLE_REAL_TIME,
                "theme": app_settings.THEME,
                "auto_refresh_stats": app_settings.AUTO_REFRESH_STATS
            },
            "validation_errors": {},
            "save_status": None
        }
        
        # Get current settings from MCP server
        try:
            async with await get_mcp_client() as mcp_client:
                health_status = await mcp_client.health_check()
                settings_data["server_available"] = health_status.status == "healthy"
                
                if settings_data["server_available"]:
                    # Get current server configuration
                    try:
                        server_config = await mcp_client.get_server_config()
                        if server_config:
                            # Update RAG strategies
                            if "rag_strategies" in server_config:
                                settings_data["current_settings"]["rag_strategies"].update(
                                    server_config["rag_strategies"]
                                )
                            
                            # Update crawling parameters
                            if "crawling" in server_config:
                                settings_data["current_settings"]["crawling_parameters"].update(
                                    server_config["crawling"]
                                )
                            
                            # Update code detection settings
                            if "code_detection" in server_config:
                                settings_data["current_settings"]["code_detection"].update(
                                    server_config["code_detection"]
                                )
                                
                    except Exception as e:
                        logger.debug(f"Could not fetch server configuration: {e}")
                        
        except ConnectionError as e:
            logger.warning(f"MCP server not available for settings: {e}")
        except Exception as e:
            logger.error(f"Error getting settings: {e}")
        
        return templates.TemplateResponse(
            "pages/settings.html", 
            {"request": request, **settings_data}
        )
        
    except Exception as e:
        logger.error(f"Settings interface error: {e}")
        raise HTTPException(status_code=500, detail="Settings interface unavailable")


@router.post("/htmx/update", response_class=HTMLResponse)
async def update_server_settings(request: Request):
    """Update server configuration settings via HTMX"""
    try:
        form_data = await request.form()
        
        # Parse form data into configuration structure
        config_update = {
            "rag_strategies": {
                "contextual_embeddings": "contextual_embeddings" in form_data,
                "hybrid_search": "hybrid_search" in form_data,
                "agentic_rag": "agentic_rag" in form_data,
                "reranking": "reranking" in form_data
            },
            "crawling": {
                "default_max_depth": int(form_data.get("default_max_depth", 3)),
                "default_max_concurrent": int(form_data.get("default_max_concurrent", 5)),
                "default_chunk_size": int(form_data.get("default_chunk_size", 1000))
            },
            "code_detection": {
                "enabled": "code_detection_enabled" in form_data,
                "min_code_length": int(form_data.get("min_code_length", 50))
            }
        }
        
        # Validate configuration
        validation_errors = _validate_settings(config_update)
        if validation_errors:
            return templates.TemplateResponse(
                "partials/settings_status.html",
                {
                    "request": request,
                    "save_status": "error",
                    "validation_errors": validation_errors
                }
            )
        
        # Update server configuration
        try:
            async with await get_mcp_client() as mcp_client:
                success = await mcp_client.update_server_config(config_update)
                
                if success:
                    # Log configuration change
                    _log_config_change(config_update, "server_settings", success=True)
                    
                    return templates.TemplateResponse(
                        "partials/settings_status.html",
                        {
                            "request": request,
                            "save_status": "success",
                            "message": "Server settings updated successfully"
                        }
                    )
                else:
                    return templates.TemplateResponse(
                        "partials/settings_status.html",
                        {
                            "request": request,
                            "save_status": "error",
                            "message": "Failed to update server settings"
                        }
                    )
                    
        except ConnectionError:
            # Save settings locally for when server reconnects
            _save_pending_config(config_update)
            return templates.TemplateResponse(
                "partials/settings_status.html",
                {
                    "request": request,
                    "save_status": "warning",
                    "message": "Settings saved locally - will apply when server reconnects"
                }
            )
            
    except Exception as e:
        logger.error(f"Settings update error: {e}")
        return templates.TemplateResponse(
            "partials/settings_status.html",
            {
                "request": request,
                "save_status": "error",
                "message": f"Settings update failed: {str(e)}"
            }
        )


@router.post("/htmx/reset", response_class=HTMLResponse)
async def reset_server_settings(request: Request):
    """Reset server settings to defaults via HTMX"""
    try:
        # Default configuration
        default_config = {
            "rag_strategies": {
                "contextual_embeddings": True,
                "hybrid_search": True,
                "agentic_rag": False,
                "reranking": True
            },
            "crawling": {
                "default_max_depth": 3,
                "default_max_concurrent": 5,
                "default_chunk_size": 1000
            },
            "code_detection": {
                "enabled": True,
                "min_code_length": 50
            }
        }
        
        # Update server configuration
        try:
            async with await get_mcp_client() as mcp_client:
                success = await mcp_client.update_server_config(default_config)
                
                if success:
                    # Log configuration change
                    _log_config_change(default_config, "reset_to_defaults", success=True)
                    
                    return templates.TemplateResponse(
                        "partials/settings_status.html",
                        {
                            "request": request,
                            "save_status": "success",
                            "message": "Settings reset to defaults successfully"
                        }
                    )
                else:
                    return templates.TemplateResponse(
                        "partials/settings_status.html",
                        {
                            "request": request,
                            "save_status": "error",
                            "message": "Failed to reset settings"
                        }
                    )
                    
        except ConnectionError:
            return templates.TemplateResponse(
                "partials/settings_status.html",
                {
                    "request": request,
                    "save_status": "error",
                    "message": "Cannot reset settings - server unavailable"
                }
            )
            
    except Exception as e:
        logger.error(f"Settings reset error: {e}")
        return templates.TemplateResponse(
            "partials/settings_status.html",
            {
                "request": request,
                "save_status": "error",
                "message": f"Settings reset failed: {str(e)}"
            }
        )


@router.post("/htmx/dashboard", response_class=HTMLResponse)
async def update_dashboard_settings(request: Request):
    """Update dashboard-specific settings via HTMX"""
    try:
        form_data = await request.form()
        
        # Parse dashboard settings
        dashboard_config = {
            "refresh_interval": int(form_data.get("refresh_interval", app_settings.REFRESH_INTERVAL)),
            "max_search_results": int(form_data.get("max_search_results", app_settings.MAX_SEARCH_RESULTS)),
            "enable_real_time": "enable_real_time" in form_data,
            "auto_refresh_stats": "auto_refresh_stats" in form_data,
            "theme": form_data.get("theme", app_settings.THEME)
        }
        
        # Validate dashboard settings
        validation_errors = _validate_dashboard_settings(dashboard_config)
        if validation_errors:
            return templates.TemplateResponse(
                "partials/settings_status.html",
                {
                    "request": request,
                    "save_status": "error",
                    "validation_errors": validation_errors
                }
            )
        
        # Update dashboard settings (in-memory for now)
        # In a real implementation, these would be saved to a config file or database
        app_settings.REFRESH_INTERVAL = dashboard_config["refresh_interval"]
        app_settings.MAX_SEARCH_RESULTS = dashboard_config["max_search_results"]
        app_settings.ENABLE_REAL_TIME = dashboard_config["enable_real_time"]
        app_settings.AUTO_REFRESH_STATS = dashboard_config["auto_refresh_stats"]
        app_settings.THEME = dashboard_config["theme"]
        
        # Log configuration change
        _log_config_change(dashboard_config, "dashboard_settings", success=True)
        
        return templates.TemplateResponse(
            "partials/settings_status.html",
            {
                "request": request,
                "save_status": "success",
                "message": "Dashboard settings updated successfully"
            }
        )
        
    except Exception as e:
        logger.error(f"Dashboard settings update error: {e}")
        return templates.TemplateResponse(
            "partials/settings_status.html",
            {
                "request": request,
                "save_status": "error",
                "message": f"Dashboard settings update failed: {str(e)}"
            }
        )




def _validate_settings(config: Dict[str, Any]) -> Dict[str, str]:
    """
    Validate server configuration settings
    
    Args:
        config: Configuration dictionary to validate
        
    Returns:
        Dictionary of validation errors (empty if valid)
    """
    errors = {}
    
    try:
        # Validate crawling parameters
        if "crawling" in config:
            crawling = config["crawling"]
            
            if "default_max_depth" in crawling:
                depth = crawling["default_max_depth"]
                if not isinstance(depth, int) or depth < 1 or depth > 10:
                    errors["default_max_depth"] = "Must be between 1 and 10"
            
            if "default_max_concurrent" in crawling:
                concurrent = crawling["default_max_concurrent"]
                if not isinstance(concurrent, int) or concurrent < 1 or concurrent > 20:
                    errors["default_max_concurrent"] = "Must be between 1 and 20"
            
            if "default_chunk_size" in crawling:
                chunk_size = crawling["default_chunk_size"]
                if not isinstance(chunk_size, int) or chunk_size < 100 or chunk_size > 10000:
                    errors["default_chunk_size"] = "Must be between 100 and 10000"
        
        # Validate code detection settings
        if "code_detection" in config:
            code_detection = config["code_detection"]
            
            if "min_code_length" in code_detection:
                min_length = code_detection["min_code_length"]
                if not isinstance(min_length, int) or min_length < 10 or min_length > 1000:
                    errors["min_code_length"] = "Must be between 10 and 1000"
    
    except Exception as e:
        logger.error(f"Settings validation error: {e}")
        errors["general"] = "Configuration validation failed"
    
    return errors


def _validate_dashboard_settings(config: Dict[str, Any]) -> Dict[str, str]:
    """
    Validate dashboard configuration settings
    
    Args:
        config: Dashboard configuration dictionary to validate
        
    Returns:
        Dictionary of validation errors (empty if valid)
    """
    errors = {}
    
    try:
        if "refresh_interval" in config:
            interval = config["refresh_interval"]
            if not isinstance(interval, int) or interval < 1000 or interval > 60000:
                errors["refresh_interval"] = "Must be between 1000 and 60000 milliseconds"
        
        if "max_search_results" in config:
            max_results = config["max_search_results"]
            if not isinstance(max_results, int) or max_results < 10 or max_results > 200:
                errors["max_search_results"] = "Must be between 10 and 200"
        
        if "theme" in config:
            theme = config["theme"]
            if theme not in ["light", "dark", "auto"]:
                errors["theme"] = "Must be 'light', 'dark', or 'auto'"
    
    except Exception as e:
        logger.error(f"Dashboard settings validation error: {e}")
        errors["general"] = "Dashboard configuration validation failed"
    
    return errors


# In-memory audit log (in production, this would be stored in a database)
_config_audit_log: List[Dict[str, Any]] = []


def _log_config_change(config: Dict[str, Any], change_type: str, success: bool = True):
    """
    Log configuration changes for audit purposes
    
    Args:
        config: Configuration that was changed
        change_type: Type of change (e.g., 'server_settings', 'dashboard_settings')
        success: Whether the change was successful
    """
    try:
        audit_entry = {
            "timestamp": datetime.now().isoformat(),
            "change_type": change_type,
            "success": success,
            "config": config,
            "summary": _generate_change_summary(config, change_type)
        }
        
        _config_audit_log.insert(0, audit_entry)  # Add to beginning
        
        # Keep only last 50 entries
        if len(_config_audit_log) > 50:
            _config_audit_log = _config_audit_log[:50]
        
        logger.info(f"Configuration change logged: {change_type} - {audit_entry['summary']}")
        
    except Exception as e:
        logger.error(f"Failed to log config change: {e}")


def _generate_change_summary(config: Dict[str, Any], change_type: str) -> str:
    """
    Generate human-readable summary of configuration changes
    
    Args:
        config: Configuration that was changed
        change_type: Type of change
        
    Returns:
        Human-readable summary string
    """
    try:
        if change_type == "reset_to_defaults":
            return "Reset all settings to default values"
        
        if change_type == "server_settings":
            changes = []
            
            if "rag_strategies" in config:
                enabled_strategies = [k for k, v in config["rag_strategies"].items() if v]
                if enabled_strategies:
                    changes.append(f"RAG strategies: {', '.join(enabled_strategies)}")
            
            if "crawling" in config:
                crawling = config["crawling"]
                crawl_changes = []
                if "default_max_depth" in crawling:
                    crawl_changes.append(f"depth={crawling['default_max_depth']}")
                if "default_max_concurrent" in crawling:
                    crawl_changes.append(f"concurrent={crawling['default_max_concurrent']}")
                if "default_chunk_size" in crawling:
                    crawl_changes.append(f"chunk_size={crawling['default_chunk_size']}")
                if crawl_changes:
                    changes.append(f"Crawling: {', '.join(crawl_changes)}")
            
            if "code_detection" in config:
                code_detection = config["code_detection"]
                enabled = code_detection.get("enabled", True)
                min_length = code_detection.get("min_code_length", 50)
                changes.append(f"Code detection: {'enabled' if enabled else 'disabled'} (min_length={min_length})")
            
            return "Updated " + "; ".join(changes) if changes else "Updated server settings"
        
        if change_type == "dashboard_settings":
            changes = []
            
            if "refresh_interval" in config:
                changes.append(f"refresh_interval={config['refresh_interval']}ms")
            if "max_search_results" in config:
                changes.append(f"max_results={config['max_search_results']}")
            if "theme" in config:
                changes.append(f"theme={config['theme']}")
            
            return "Updated dashboard: " + ", ".join(changes) if changes else "Updated dashboard settings"
        
        return f"Updated {change_type}"
        
    except Exception as e:
        logger.error(f"Failed to generate change summary: {e}")
        return f"Updated {change_type}"


def _get_recent_config_changes() -> List[Dict[str, Any]]:
    """
    Get recent configuration changes for audit log display
    
    Returns:
        List of recent configuration changes
    """
    try:
        # Return formatted audit entries for display
        formatted_entries = []
        
        for entry in _config_audit_log[:20]:  # Last 20 entries
            formatted_entry = {
                "timestamp": entry["timestamp"],
                "change_type": entry["change_type"].replace("_", " ").title(),
                "success": entry["success"],
                "summary": entry["summary"],
                "time_ago": _format_time_ago(entry["timestamp"])
            }
            formatted_entries.append(formatted_entry)
        
        return formatted_entries
        
    except Exception as e:
        logger.error(f"Failed to get recent config changes: {e}")
        return []


def _format_time_ago(timestamp_str: str) -> str:
    """
    Format timestamp as 'time ago' string
    
    Args:
        timestamp_str: ISO format timestamp string
        
    Returns:
        Human-readable 'time ago' string
    """
    try:
        timestamp = datetime.fromisoformat(timestamp_str)
        now = datetime.now()
        diff = now - timestamp
        
        if diff.days > 0:
            return f"{diff.days} day{'s' if diff.days != 1 else ''} ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hour{'s' if hours != 1 else ''} ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
        else:
            return "Just now"
            
    except Exception as e:
        logger.error(f"Failed to format time ago: {e}")
        return "Unknown"


# Pending configuration storage (for when server is unavailable)
_pending_config: Dict[str, Any] = {}


def _save_pending_config(config: Dict[str, Any]):
    """
    Save configuration changes that couldn't be applied due to server unavailability
    
    Args:
        config: Configuration to save for later application
    """
    try:
        global _pending_config
        _pending_config.update(config)
        logger.info("Configuration saved as pending - will apply when server reconnects")
        
    except Exception as e:
        logger.error(f"Failed to save pending config: {e}")


def _get_pending_config() -> Dict[str, Any]:
    """
    Get pending configuration changes
    
    Returns:
        Dictionary of pending configuration changes
    """
    return _pending_config.copy()


def _clear_pending_config():
    """Clear pending configuration changes"""
    global _pending_config
    _pending_config = {}