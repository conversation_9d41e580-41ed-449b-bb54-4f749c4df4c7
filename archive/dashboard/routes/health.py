"""
System health and monitoring routes
"""
from fastapi import APIRouter, Request
from fastapi.responses import JSONResponse

from ..mcp_client import get_mcp_client, transform_health_status_for_ui
from ..templates_config import templates

router = APIRouter()


@router.get("/")
async def health_dashboard(request: Request):
    """Health monitoring page"""
    # Get MCP server health status
    async with await get_mcp_client() as mcp_client:
        health_status = await mcp_client.health_check()
        connection_info = mcp_client.get_connection_info()
    
    # Transform for UI display
    health_data = transform_health_status_for_ui(health_status)
    
    return templates.TemplateResponse(
        "pages/health.html", 
        {
            "request": request, 
            "title": "System Health",
            "health": health_data,
            "connection": connection_info
        }
    )


@router.get("/api/status")
async def health_api(request: Request):
    """API endpoint for health status (for HTMX updates)"""
    try:
        async with await get_mcp_client() as mcp_client:
            health_status = await mcp_client.health_check()
            server_stats = await mcp_client.get_server_stats()
        
        # Transform for UI display
        health_data = transform_health_status_for_ui(health_status)
        
        return JSONResponse({
            "success": True,
            "health": health_data,
            "stats": server_stats
        })
    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": str(e)
        }, status_code=500)