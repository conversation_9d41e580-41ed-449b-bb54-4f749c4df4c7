/**
 * Client-side form validation for enhanced UX and security
 * Provides real-time validation feedback before server submission
 */

class FormValidator {
    constructor() {
        this.patterns = {
            url: /^https?:\/\/[^\s<>"{}|\\^`\[\]]+$/i,
            email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            alphanumeric: /^[a-zA-Z0-9._-]+$/,
            safeText: /^[a-zA-Z0-9\s._,!?-]+$/
        };
        
        this.maxLengths = {
            url: 2048,
            searchQuery: 500,
            sourceName: 255,
            title: 200,
            description: 1000
        };
        
        this.init();
    }
    
    init() {
        // Initialize validation on page load
        document.addEventListener('DOMContentLoaded', () => {
            this.attachValidationListeners();
            this.setupHTMXValidation();
        });
    }
    
    attachValidationListeners() {
        // Attach validation to all forms with validation class
        const forms = document.querySelectorAll('.validate-form');
        forms.forEach(form => this.setupFormValidation(form));
        
        // Attach validation to specific input types
        this.setupInputValidation();
    }
    
    setupFormValidation(form) {
        // Real-time validation on input
        form.addEventListener('input', (e) => {
            if (e.target.matches('input, textarea, select')) {
                this.validateField(e.target);
            }
        });
        
        // Validation on form submission
        form.addEventListener('submit', (e) => {
            if (!this.validateForm(form)) {
                e.preventDefault();
                this.showFormErrors(form);
                return false;
            }
        });
        
        // Clear validation on focus
        form.addEventListener('focus', (e) => {
            if (e.target.matches('input, textarea, select')) {
                this.clearFieldError(e.target);
            }
        }, true);
    }
    
    setupInputValidation() {
        // URL validation
        document.querySelectorAll('input[type="url"], input[name*="url"]').forEach(input => {
            input.addEventListener('blur', () => this.validateURL(input));
        });
        
        // Search query validation
        document.querySelectorAll('input[name*="query"], input[name*="search"]').forEach(input => {
            input.addEventListener('input', () => this.validateSearchQuery(input));
        });
        
        // Numeric validation
        document.querySelectorAll('input[type="number"]').forEach(input => {
            input.addEventListener('input', () => this.validateNumeric(input));
        });
        
        // Source name validation
        document.querySelectorAll('input[name*="source"]').forEach(input => {
            input.addEventListener('input', () => this.validateSourceName(input));
        });
    }
    
    setupHTMXValidation() {
        // Add validation before HTMX requests
        document.addEventListener('htmx:configRequest', (e) => {
            const form = e.target.closest('form');
            if (form && form.classList.contains('validate-form')) {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                    this.showFormErrors(form);
                    return false;
                }
            }
        });
        
        // Handle HTMX validation errors
        document.addEventListener('htmx:responseError', (e) => {
            this.handleServerValidationError(e);
        });
    }
    
    validateField(field) {
        const fieldType = this.getFieldType(field);
        let isValid = true;
        let errorMessage = '';
        
        switch (fieldType) {
            case 'url':
                [isValid, errorMessage] = this.validateURLField(field);
                break;
            case 'search':
                [isValid, errorMessage] = this.validateSearchField(field);
                break;
            case 'numeric':
                [isValid, errorMessage] = this.validateNumericField(field);
                break;
            case 'source':
                [isValid, errorMessage] = this.validateSourceField(field);
                break;
            case 'required':
                [isValid, errorMessage] = this.validateRequired(field);
                break;
            default:
                [isValid, errorMessage] = this.validateGeneral(field);
        }
        
        this.showFieldValidation(field, isValid, errorMessage);
        return isValid;
    }
    
    getFieldType(field) {
        if (field.type === 'url' || field.name.includes('url')) return 'url';
        if (field.name.includes('query') || field.name.includes('search')) return 'search';
        if (field.type === 'number') return 'numeric';
        if (field.name.includes('source')) return 'source';
        if (field.required) return 'required';
        return 'general';
    }
    
    validateURLField(field) {
        const value = field.value.trim();
        
        if (!value && !field.required) return [true, ''];
        if (!value && field.required) return [false, 'URL is required'];
        
        if (value.length > this.maxLengths.url) {
            return [false, `URL too long (max ${this.maxLengths.url} characters)`];
        }
        
        if (!this.patterns.url.test(value)) {
            return [false, 'Please enter a valid HTTP or HTTPS URL'];
        }
        
        // Check for dangerous schemes
        if (/^(javascript|data|vbscript):/i.test(value)) {
            return [false, 'Invalid URL scheme'];
        }
        
        return [true, ''];
    }
    
    validateSearchField(field) {
        const value = field.value.trim();
        
        if (!value && !field.required) return [true, ''];
        if (!value && field.required) return [false, 'Search query is required'];
        
        if (value.length < 2) {
            return [false, 'Query too short (minimum 2 characters)'];
        }
        
        if (value.length > this.maxLengths.searchQuery) {
            return [false, `Query too long (max ${this.maxLengths.searchQuery} characters)`];
        }
        
        // Check for potential injection patterns
        const dangerousPatterns = [
            /<script/i,
            /javascript:/i,
            /on\w+\s*=/i,
            /(union|select|insert|delete|drop)\s+/i,
            /[<>'"&]/
        ];
        
        for (const pattern of dangerousPatterns) {
            if (pattern.test(value)) {
                return [false, 'Query contains invalid characters'];
            }
        }
        
        return [true, ''];
    }
    
    validateNumericField(field) {
        const value = field.value.trim();
        
        if (!value && !field.required) return [true, ''];
        if (!value && field.required) return [false, 'This field is required'];
        
        const num = parseFloat(value);
        if (isNaN(num)) {
            return [false, 'Please enter a valid number'];
        }
        
        const min = parseFloat(field.min);
        const max = parseFloat(field.max);
        
        if (!isNaN(min) && num < min) {
            return [false, `Value must be at least ${min}`];
        }
        
        if (!isNaN(max) && num > max) {
            return [false, `Value must be no more than ${max}`];
        }
        
        return [true, ''];
    }
    
    validateSourceField(field) {
        const value = field.value.trim();
        
        if (!value && !field.required) return [true, ''];
        if (!value && field.required) return [false, 'Source name is required'];
        
        if (value.length > this.maxLengths.sourceName) {
            return [false, `Source name too long (max ${this.maxLengths.sourceName} characters)`];
        }
        
        if (!this.patterns.alphanumeric.test(value)) {
            return [false, 'Source name can only contain letters, numbers, dots, hyphens, and underscores'];
        }
        
        // Check for path traversal
        if (value.includes('..') || value.includes('/') || value.includes('\\')) {
            return [false, 'Source name contains invalid characters'];
        }
        
        return [true, ''];
    }
    
    validateRequired(field) {
        const value = field.value.trim();
        
        if (!value) {
            return [false, 'This field is required'];
        }
        
        return [true, ''];
    }
    
    validateGeneral(field) {
        const value = field.value.trim();
        
        if (!value && !field.required) return [true, ''];
        if (!value && field.required) return [false, 'This field is required'];
        
        // Basic XSS prevention
        if (/<script|javascript:|on\w+\s*=/i.test(value)) {
            return [false, 'Field contains invalid characters'];
        }
        
        return [true, ''];
    }
    
    showFieldValidation(field, isValid, errorMessage) {
        const fieldGroup = field.closest('.form-group') || field.parentElement;
        
        // Remove existing validation classes and messages
        field.classList.remove('field-valid', 'field-invalid');
        const existingError = fieldGroup.querySelector('.field-error');
        if (existingError) existingError.remove();
        
        if (!isValid && errorMessage) {
            // Add error styling
            field.classList.add('field-invalid');
            
            // Show error message
            const errorElement = document.createElement('div');
            errorElement.className = 'field-error text-red-600 text-sm mt-1';
            errorElement.textContent = errorMessage;
            fieldGroup.appendChild(errorElement);
        } else if (field.value.trim()) {
            // Add valid styling for non-empty fields
            field.classList.add('field-valid');
        }
    }
    
    clearFieldError(field) {
        field.classList.remove('field-invalid');
        const fieldGroup = field.closest('.form-group') || field.parentElement;
        const existingError = fieldGroup.querySelector('.field-error');
        if (existingError) existingError.remove();
    }
    
    validateForm(form) {
        const fields = form.querySelectorAll('input, textarea, select');
        let isValid = true;
        
        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    showFormErrors(form) {
        const firstInvalidField = form.querySelector('.field-invalid');
        if (firstInvalidField) {
            firstInvalidField.focus();
            firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
        
        // Show general form error if no specific field errors
        if (!firstInvalidField) {
            this.showFormMessage(form, 'Please correct the errors below', 'error');
        }
    }
    
    showFormMessage(form, message, type = 'info') {
        // Remove existing form messages
        const existingMessage = form.querySelector('.form-message');
        if (existingMessage) existingMessage.remove();
        
        // Create new message
        const messageElement = document.createElement('div');
        messageElement.className = `form-message alert alert-${type} mb-4`;
        messageElement.innerHTML = `
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    ${type === 'error' ? 
                        '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>' :
                        '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>'
                    }
                </svg>
                <span>${message}</span>
            </div>
        `;
        
        // Insert at top of form
        form.insertBefore(messageElement, form.firstChild);
        
        // Auto-hide after 5 seconds for non-error messages
        if (type !== 'error') {
            setTimeout(() => {
                if (messageElement.parentNode) {
                    messageElement.remove();
                }
            }, 5000);
        }
    }
    
    handleServerValidationError(event) {
        const form = event.target.closest('form');
        if (!form) return;
        
        try {
            const response = event.detail.xhr.responseText;
            if (response.includes('validation') || response.includes('invalid')) {
                this.showFormMessage(form, 'Server validation failed. Please check your input.', 'error');
            }
        } catch (e) {
            console.error('Error handling server validation:', e);
        }
    }
    
    // Utility methods for manual validation
    validateURL(input) {
        return this.validateURLField(input);
    }
    
    validateSearchQuery(input) {
        return this.validateSearchField(input);
    }
    
    validateNumeric(input) {
        return this.validateNumericField(input);
    }
    
    validateSourceName(input) {
        return this.validateSourceField(input);
    }
}

// Initialize form validator
const formValidator = new FormValidator();

// Export for use in other scripts
window.FormValidator = FormValidator;
window.formValidator = formValidator;