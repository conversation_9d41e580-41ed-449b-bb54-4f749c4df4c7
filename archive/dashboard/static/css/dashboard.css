/* Custom CSS for RAG Pipeline Dashboard */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* Firebase Colors */
  --firebase-orange: #FF9100;
  --firebase-yellow: #FFC400;
  --firebase-red: #DD2C00;
  --android-green: #C6FF00;
  --google-blue: #4285F4;
  
  /* Dark Theme */
  --deep-dark: #121212;
  --elevated-dark: #1E1E1E;
  --surface-dark: #2D2D2D;
  --text-primary: #FFFFFF;
  --text-secondary: #B3B3B3;
  --text-muted: #737373;
  
  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  background-color: var(--deep-dark) !important;
  color: var(--text-primary) !important;
  line-height: 1.6;
}

/* Override any conflicting styles */
[data-theme="dark"], [data-theme="light"] {
  background-color: var(--deep-dark) !important;
  color: var(--text-primary) !important;
}

/* Layout */
.dashboard-layout {
  display: grid;
  grid-template-columns: 280px 1fr 320px;
  min-height: 100vh;
}

.sidebar {
  background-color: var(--elevated-dark) !important;
  padding: var(--spacing-lg);
  border-right: 1px solid var(--surface-dark);
}

.main-content {
  background-color: var(--deep-dark) !important;
  padding: var(--spacing-lg);
  overflow-y: auto;
}

.right-panel {
  background-color: var(--elevated-dark) !important;
  padding: var(--spacing-lg);
  border-left: 1px solid var(--surface-dark);
}

/* Navigation */
.nav-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.nav-logo {
  width: 32px;
  height: 32px;
  background-color: var(--firebase-orange);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
}

.nav-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary) !important;
}

.nav-menu {
  list-style: none;
}

.nav-item {
  margin-bottom: var(--spacing-sm);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  color: var(--text-secondary) !important;
  text-decoration: none;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.nav-link:hover,
.nav-link.active {
  background-color: var(--surface-dark) !important;
  color: var(--text-primary) !important;
}

.nav-link.active {
  border-left: 3px solid var(--firebase-orange);
}

/* Cards */
.card {
  background-color: var(--elevated-dark) !important;
  border: 1px solid var(--surface-dark);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  transition: all 0.2s ease;
}

.card:hover {
  border-color: var(--firebase-orange);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary) !important;
}

.card-subtitle {
  font-size: 14px;
  color: var(--text-secondary) !important;
  margin-bottom: var(--spacing-sm);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-sm);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-primary {
  background-color: var(--firebase-orange);
  color: white !important;
}

.btn-primary:hover {
  background-color: #E68200;
}

.btn-secondary {
  background-color: var(--surface-dark);
  color: var(--text-primary) !important;
  border: 1px solid var(--surface-dark);
}

.btn-secondary:hover {
  background-color: #3D3D3D;
}

.btn-danger {
  background-color: var(--firebase-red);
  color: white !important;
}

.btn-danger:hover {
  background-color: #C62400;
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 12px;
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 500;
}

.status-completed {
  background-color: #C6FF0020;
  color: var(--android-green) !important;
}

.status-processing {
  background-color: #FFC40020;
  color: var(--firebase-yellow) !important;
}

.status-pending {
  background-color: #4285F420;
  color: var(--google-blue) !important;
}

.status-failed {
  background-color: #DD2C0020;
  color: var(--firebase-red) !important;
}

/* Form Elements */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary) !important;
}

.form-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--surface-dark) !important;
  border: 1px solid var(--surface-dark);
  border-radius: var(--radius-sm);
  color: var(--text-primary) !important;
  font-size: 14px;
}

.form-input:focus {
  outline: none;
  border-color: var(--firebase-orange);
}

.form-select {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--surface-dark) !important;
  border: 1px solid var(--surface-dark);
  border-radius: var(--radius-sm);
  color: var(--text-primary) !important;
  font-size: 14px;
}

.form-textarea {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--surface-dark) !important;
  border: 1px solid var(--surface-dark);
  border-radius: var(--radius-sm);
  color: var(--text-primary) !important;
  font-size: 14px;
  resize: vertical;
  min-height: 100px;
}

/* Table */
.table-container {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--surface-dark);
}

.table th {
  background-color: var(--surface-dark) !important;
  color: var(--text-primary) !important;
  font-weight: 600;
  font-size: 14px;
}

.table td {
  color: var(--text-secondary) !important;
  font-size: 14px;
}

.table tbody tr:hover {
  background-color: var(--surface-dark) !important;
}

/* Tags */
.tag {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--surface-dark);
  color: var(--text-secondary) !important;
  border-radius: var(--radius-sm);
  font-size: 12px;
  margin-right: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
}

.tag-primary {
  background-color: var(--firebase-orange);
  color: white !important;
}

/* Metrics */
.metric-card {
  background-color: var(--elevated-dark) !important;
  border: 1px solid var(--surface-dark);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  text-align: center;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--firebase-orange) !important;
  margin-bottom: var(--spacing-xs);
}

.metric-label {
  font-size: 14px;
  color: var(--text-secondary) !important;
}

/* Progress Bar */
.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--surface-dark);
  border-radius: var(--radius-sm);
  overflow: hidden;
  margin-bottom: var(--spacing-sm);
}

.progress-fill {
  height: 100%;
  background-color: var(--firebase-orange);
  transition: width 0.3s ease;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--elevated-dark) !important;
  border: 1px solid var(--surface-dark);
  border-radius: var(--radius-md);
  padding: var(--spacing-xl);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary) !important;
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-secondary) !important;
  font-size: 24px;
  cursor: pointer;
}

/* Floating Action Button */
.fab {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 56px;
  height: 56px;
  background-color: var(--firebase-orange);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 24px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
  z-index: 100;
}

.fab:hover {
  background-color: #E68200;
  transform: scale(1.1);
}

/* Firebase-inspired Component Styles */
.firebase-card {
  background-color: var(--elevated-dark) !important;
  border: 1px solid var(--surface-dark);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  transition: all 0.2s ease;
}

.firebase-card:hover {
  border-color: var(--firebase-orange);
  transform: translateY(-2px);
}

.firebase-card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary) !important;
  margin: 0;
}

.firebase-card-subtitle {
  font-size: 14px;
  color: var(--text-secondary) !important;
  margin-bottom: var(--spacing-sm);
}

.firebase-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-lg);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.firebase-btn-primary {
  background-color: var(--firebase-orange);
  color: white !important;
}

.firebase-btn-primary:hover {
  background-color: #E68200;
  transform: translateY(-1px);
}

.firebase-btn-secondary {
  background-color: var(--surface-dark);
  color: var(--text-primary) !important;
  border: 1px solid var(--surface-dark);
}

.firebase-btn-secondary:hover {
  background-color: #3D3D3D;
  border-color: var(--firebase-orange);
}

.firebase-btn-danger {
  background-color: var(--firebase-red);
  color: white !important;
}

.firebase-btn-danger:hover {
  background-color: #C62400;
  transform: translateY(-1px);
}

.firebase-status {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 500;
}

.firebase-status-success {
  background-color: rgba(198, 255, 0, 0.12);
  color: var(--android-green) !important;
  border: 1px solid rgba(198, 255, 0, 0.2);
}

.firebase-status-processing {
  background-color: rgba(255, 196, 0, 0.12);
  color: var(--firebase-yellow) !important;
  border: 1px solid rgba(255, 196, 0, 0.2);
}

.firebase-status-pending {
  background-color: rgba(66, 133, 244, 0.12);
  color: var(--google-blue) !important;
  border: 1px solid rgba(66, 133, 244, 0.2);
}

.firebase-status-error {
  background-color: rgba(221, 44, 0, 0.12);
  color: var(--firebase-red) !important;
  border: 1px solid rgba(221, 44, 0, 0.2);
}

.firebase-nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--text-secondary) !important;
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all 0.2s ease;
  font-weight: 500;
}

.firebase-nav-link:hover {
  background-color: var(--surface-dark) !important;
  color: var(--text-primary) !important;
}

.firebase-nav-link.active {
  background-color: var(--surface-dark) !important;
  color: var(--firebase-orange) !important;
  border-left: 3px solid var(--firebase-orange);
}

.firebase-metric-card {
  background-color: var(--elevated-dark) !important;
  border: 1px solid var(--surface-dark);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  text-align: center;
  transition: all 0.2s ease;
}

.firebase-metric-card:hover {
  border-color: var(--firebase-orange);
  transform: translateY(-2px);
}

.firebase-metric-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--firebase-orange) !important;
  margin-bottom: var(--spacing-sm);
}

.firebase-metric-label {
  font-size: 14px;
  color: var(--text-secondary) !important;
  font-weight: 500;
}

/* Override Tailwind classes */
.bg-white {
  background-color: var(--elevated-dark) !important;
}

.bg-gray-50 {
  background-color: var(--deep-dark) !important;
}

.bg-gray-100 {
  background-color: var(--surface-dark) !important;
}

.text-gray-900 {
  color: var(--text-primary) !important;
}

.text-gray-600 {
  color: var(--text-secondary) !important;
}

.text-gray-500 {
  color: var(--text-muted) !important;
}

.border-gray-200 {
  border-color: var(--surface-dark) !important;
}

.border-gray-300 {
  border-color: var(--surface-dark) !important;
}

/* HTMX loading indicators */
.htmx-indicator {
    display: none;
}

.htmx-request .htmx-indicator {
    display: inline;
}

.htmx-request.htmx-indicator {
    display: inline;
}

/* Custom animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* Enhanced Loading States */
.loading-state {
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--elevated-dark);
    border-radius: var(--radius-md);
    border: 1px dashed var(--surface-dark);
}

.spinner {
    border: 2px solid var(--surface-dark);
    border-top: 2px solid var(--firebase-orange);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

.spinner-lg {
    width: 32px;
    height: 32px;
    border-width: 3px;
}

.skeleton {
    background: linear-gradient(90deg, var(--surface-dark) 25%, var(--elevated-dark) 50%, var(--surface-dark) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--radius-sm);
}

.skeleton-text {
    height: 1rem;
    margin-bottom: 0.5rem;
}

.skeleton-text.w-full { width: 100%; }
.skeleton-text.w-3-4 { width: 75%; }
.skeleton-text.w-1-2 { width: 50%; }
.skeleton-text.w-1-4 { width: 25%; }

.skeleton-card {
    padding: var(--spacing-lg);
    height: 120px;
}

/* Error States */
.error-boundary {
    border-left: 4px solid var(--firebase-red);
    background: rgba(221, 44, 0, 0.05);
}

.error-boundary .btn {
    background: var(--firebase-red);
    color: white;
    border: none;
}

.error-boundary .btn:hover {
    background: #C62400;
}

/* Success States */
.success-notification {
    border-left: 4px solid var(--android-green);
    background: rgba(198, 255, 0, 0.05);
}

/* Warning States */
.warning-notification {
    border-left: 4px solid var(--firebase-yellow);
    background: rgba(255, 196, 0, 0.05);
}

/* Progress States */
.progress-container {
    background: var(--elevated-dark);
    border-radius: var(--radius-md);
    border: 1px solid var(--surface-dark);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--surface-dark);
    border-radius: var(--radius-sm);
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--firebase-orange), var(--firebase-yellow));
    transition: width 0.3s ease;
    border-radius: var(--radius-sm);
}

.progress-indeterminate {
    position: relative;
    background: var(--surface-dark);
    overflow: hidden;
}

.progress-indeterminate::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: linear-gradient(90deg, transparent, var(--firebase-orange), transparent);
    animation: progress-slide 1.5s infinite;
}

/* HTMX Enhanced Loading Indicators */
.htmx-indicator {
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.htmx-request .htmx-indicator {
    display: inline-flex;
    opacity: 1;
}

.htmx-request.htmx-indicator {
    display: inline-flex;
    opacity: 1;
}

/* Loading overlay for forms */
.htmx-request .form-overlay {
    position: relative;
}

.htmx-request .form-overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(18, 18, 18, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    z-index: 10;
}

.htmx-request .form-overlay::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 32px;
    height: 32px;
    border: 3px solid var(--surface-dark);
    border-top: 3px solid var(--firebase-orange);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 11;
}

/* Button loading states */
.btn.loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid currentColor;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    opacity: 0.7;
}

/* Table loading states */
.table-loading {
    position: relative;
    min-height: 200px;
}

.table-loading .skeleton-row {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--surface-dark);
}

.table-loading .skeleton-cell {
    height: 1rem;
    border-radius: var(--radius-sm);
}

/* Card loading states */
.card-loading {
    background: var(--elevated-dark);
    border: 1px solid var(--surface-dark);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    min-height: 120px;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

@keyframes progress-slide {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Error shake animation */
.error-shake {
    animation: shake 0.5s ease-in-out;
}

/* Status indicators with enhanced states */
.status-loading {
    background-color: #4285F420;
    color: var(--google-blue) !important;
    position: relative;
}

.status-loading::after {
    content: '';
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border: 2px solid currentColor;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Accessibility improvements */
.loading-state[role="status"]::after {
    content: "Loading...";
    position: absolute;
    left: -9999px;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

/* Responsive loading states */
@media (max-width: 768px) {
    .loading-state {
        min-height: 80px;
    }
    
    .spinner-lg {
        width: 24px;
        height: 24px;
        border-width: 2px;
    }
    
    .progress-container {
        padding: var(--spacing-md);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-layout {
    grid-template-columns: 1fr;
  }
  
  .sidebar,
  .right-panel {
    display: none;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: var(--spacing-md);
  }
  
  .card {
    padding: var(--spacing-md);
  }
  
  .modal-content {
    padding: var(--spacing-lg);
  }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-muted { color: var(--text-muted) !important; }
.text-primary { color: var(--text-primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.align-center { align-items: center; }
.gap-1 { gap: var(--spacing-xs); }
.gap-2 { gap: var(--spacing-sm); }
.gap-3 { gap: var(--spacing-md); }

.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }

/* Form Validation Styles */
.field-valid {
  border-color: var(--android-green) !important;
  box-shadow: 0 0 0 1px rgba(198, 255, 0, 0.3);
}

.field-invalid {
  border-color: var(--firebase-red) !important;
  box-shadow: 0 0 0 1px rgba(221, 44, 0, 0.3);
  animation: shake 0.3s ease-in-out;
}

.field-error {
  color: var(--firebase-red) !important;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.field-error::before {
  content: "⚠";
  font-size: 0.75rem;
}

.form-message {
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-md);
  animation: fadeIn 0.3s ease;
}

.alert-error {
  background-color: rgba(221, 44, 0, 0.1);
  border: 1px solid rgba(221, 44, 0, 0.3);
  color: var(--firebase-red);
}

.alert-success {
  background-color: rgba(198, 255, 0, 0.1);
  border: 1px solid rgba(198, 255, 0, 0.3);
  color: var(--android-green);
}

.alert-info {
  background-color: rgba(66, 133, 244, 0.1);
  border: 1px solid rgba(66, 133, 244, 0.3);
  color: var(--google-blue);
}

/* Validation form classes */
.validate-form .form-input:focus {
  border-color: var(--firebase-orange);
  box-shadow: 0 0 0 1px rgba(255, 145, 0, 0.3);
}

.validate-form .form-input.field-valid:focus {
  border-color: var(--android-green);
  box-shadow: 0 0 0 1px rgba(198, 255, 0, 0.3);
}

.validate-form .form-input.field-invalid:focus {
  border-color: var(--firebase-red);
  box-shadow: 0 0 0 1px rgba(221, 44, 0, 0.3);
}

/* Real-time validation feedback */
.form-group {
  position: relative;
  margin-bottom: var(--spacing-md);
}

.form-group.has-validation {
  margin-bottom: calc(var(--spacing-md) + 1.5rem);
}

/* Validation icons */
.field-valid + .validation-icon::after {
  content: "✓";
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--android-green);
  font-weight: bold;
  pointer-events: none;
}

.field-invalid + .validation-icon::after {
  content: "✗";
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--firebase-red);
  font-weight: bold;
  pointer-events: none;
}

/* Toast Notifications */
.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: var(--elevated-dark) !important;
  border: 1px solid var(--surface-dark);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  max-width: 400px;
  z-index: 1001;
  animation: slideIn 0.3s ease;
}

.toast-success {
  border-left: 4px solid var(--android-green);
}

.toast-error {
  border-left: 4px solid var(--firebase-red);
}

.toast-info {
  border-left: 4px solid var(--google-blue);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}