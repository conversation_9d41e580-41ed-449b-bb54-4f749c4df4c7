<!-- Firebase-inspired status indicator components -->
{% macro status_badge(status, text=None, size="md", class="") %}
{% set status_classes = {
    "online": "firebase-status-success",
    "offline": "firebase-status-error",
    "warning": "firebase-status-processing",
    "loading": "firebase-status-pending",
    "unknown": "firebase-status firebase-status-pending"
} %}
{% set size_classes = {
    "sm": "text-xs",
    "md": "text-sm",
    "lg": "text-base"
} %}
{% set display_text = text or status.title() %}

<span class="firebase-status {{ status_classes[status] }} {{ size_classes[size] }} {{ class }}"
      role="status"
      aria-label="{{ display_text }} status">
    <svg class="w-2 h-2 mr-1.5 {% if status == 'loading' %}animate-pulse{% endif %}" 
         fill="currentColor" 
         viewBox="0 0 8 8"
         aria-hidden="true">
        <circle cx="4" cy="4" r="3" />
    </svg>
    {{ display_text }}
</span>
{% endmacro %}

<!-- Health status component -->
{% macro health_status(component_name, status, details=None, last_check=None) %}
<div class="flex items-center justify-between py-3 px-4 bg-white rounded-lg border border-gray-200">
    <div class="flex items-center min-w-0 flex-1">
        <div class="flex-shrink-0">
            {% if status == 'healthy' %}
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            {% elif status == 'unhealthy' %}
            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </div>
            {% elif status == 'warning' %}
            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            {% else %}
            <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            {% endif %}
        </div>
        
        <div class="ml-4 min-w-0 flex-1">
            <p class="text-sm font-medium text-gray-900">{{ component_name }}</p>
            {% if details %}
            <p class="text-sm text-gray-500 truncate">{{ details }}</p>
            {% endif %}
            {% if last_check %}
            <p class="text-xs text-gray-400 mt-1">
                Last checked: <time datetime="{{ last_check.isoformat() }}">{{ last_check.strftime('%H:%M:%S') }}</time>
            </p>
            {% endif %}
        </div>
    </div>
    
    <div class="flex-shrink-0 ml-4">
        {{ status_badge(status) }}
    </div>
</div>
{% endmacro %}

<!-- Firebase-inspired progress bar component -->
{% macro progress_bar(value, max_value=100, label=None, show_percentage=True, color="primary", size="md", class="") %}
{% set percentage = (value / max_value * 100) if max_value > 0 else 0 %}
{% set color_classes = {
    "primary": "firebase-progress-fill",
    "success": "bg-gradient-to-r from-android-green to-android-green",
    "warning": "bg-gradient-to-r from-firebase-yellow to-firebase-orange",
    "danger": "bg-gradient-to-r from-firebase-red to-firebase-red"
} %}
{% set size_classes = {
    "sm": "h-2",
    "md": "h-3",
    "lg": "h-4"
} %}

<div class="{{ class }}">
    {% if label or show_percentage %}
    <div class="flex justify-between text-sm font-medium mb-1" style="color: var(--text-primary);">
        {% if label %}<span>{{ label }}</span>{% endif %}
        {% if show_percentage %}<span>{{ "%.1f"|format(percentage) }}%</span>{% endif %}
    </div>
    {% endif %}
    
    <div class="firebase-progress {{ size_classes[size] }}" 
         role="progressbar" 
         aria-valuenow="{{ value }}" 
         aria-valuemin="0" 
         aria-valuemax="{{ max_value }}"
         {% if label %}aria-label="{{ label }}"{% endif %}>
        <div class="{{ color_classes[color] }} {{ size_classes[size] }} rounded-full transition-all duration-300 ease-in-out" 
             style="width: {{ percentage }}%"></div>
    </div>
</div>
{% endmacro %}

<!-- Loading spinner component -->
{% macro loading_spinner(size="md", text=None, class="") %}
{% set size_classes = {
    "sm": "w-4 h-4",
    "md": "w-6 h-6",
    "lg": "w-8 h-8",
    "xl": "w-12 h-12"
} %}

<div class="flex items-center justify-center {{ class }}" role="status" aria-label="Loading">
    <svg class="animate-spin {{ size_classes[size] }} text-primary-600" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    {% if text %}
    <span class="ml-2 text-sm text-gray-600">{{ text }}</span>
    {% endif %}
    <span class="sr-only">Loading...</span>
</div>
{% endmacro %}