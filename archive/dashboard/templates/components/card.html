<!-- Firebase-inspired reusable card component -->
{% macro card(title=None, subtitle=None, icon=None, class="", header_actions=None, footer=None) %}
<div class="firebase-card {{ class }}">
    {% if title or icon or header_actions %}
    <div class="firebase-card-header">
        <div class="flex items-center min-w-0 flex-1">
            {% if icon %}
            <div class="flex-shrink-0 mr-3">
                {{ icon | safe }}
            </div>
            {% endif %}
            <div class="min-w-0 flex-1">
                {% if title %}
                <h3 class="firebase-card-title">{{ title }}</h3>
                {% endif %}
                {% if subtitle %}
                <p class="firebase-card-subtitle">{{ subtitle }}</p>
                {% endif %}
            </div>
        </div>
        {% if header_actions %}
        <div class="flex-shrink-0 ml-4">
            {{ header_actions | safe }}
        </div>
        {% endif %}
    </div>
    {% endif %}
    
    <div style="padding: var(--spacing-md);">
        {{ caller() }}
    </div>
    
    {% if footer %}
    <div style="padding: var(--spacing-md); border-top: 1px solid var(--border-color); background-color: var(--bg-elevated); margin-top: var(--spacing-md);">
        {{ footer | safe }}
    </div>
    {% endif %}
</div>
{% endmacro %}

<!-- Firebase-inspired metric card component -->
{% macro metric_card(title, value, change=None, change_type=None, icon=None, description=None, class="") %}
<div class="firebase-metric-card {{ class }}">
    <div class="flex items-center">
        {% if icon %}
        <div class="flex-shrink-0">
            <div class="w-8 h-8 rounded-lg flex items-center justify-center" style="background-color: rgba(255, 145, 0, 0.1);">
                {{ icon | safe }}
            </div>
        </div>
        {% endif %}
        <div class="{% if icon %}ml-4{% endif %} flex-1 min-w-0">
            <p class="firebase-metric-label truncate">{{ title }}</p>
            <div class="flex items-baseline">
                <p class="firebase-metric-value">{{ value }}</p>
                {% if change %}
                <p class="ml-2 flex items-baseline firebase-metric-change 
                         {% if change_type == 'increase' %}positive{% elif change_type == 'decrease' %}negative{% endif %}">
                    {% if change_type == 'increase' %}
                    <svg class="self-center flex-shrink-0 h-4 w-4" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true" style="color: var(--android-green);">
                        <path fill-rule="evenodd" d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z" clip-rule="evenodd" />
                    </svg>
                    {% elif change_type == 'decrease' %}
                    <svg class="self-center flex-shrink-0 h-4 w-4" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true" style="color: var(--firebase-red);">
                        <path fill-rule="evenodd" d="M10 3a.75.75 0 01.75.75v10.638l3.96-4.158a.75.75 0 111.08 1.04l-5.25 5.5a.75.75 0 01-1.08 0l-5.25-5.5a.75.75 0 111.08-1.04L9.25 14.388V3.75A.75.75 0 0110 3z" clip-rule="evenodd" />
                    </svg>
                    {% endif %}
                    <span class="sr-only">{% if change_type == 'increase' %}Increased{% elif change_type == 'decrease' %}Decreased{% else %}Changed{% endif %} by</span>
                    {{ change }}
                </p>
                {% endif %}
            </div>
            {% if description %}
            <p class="text-sm mt-1" style="color: var(--text-secondary);">{{ description }}</p>
            {% endif %}
        </div>
    </div>
</div>
{% endmacro %}