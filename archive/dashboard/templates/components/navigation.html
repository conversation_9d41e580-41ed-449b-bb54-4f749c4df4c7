<!-- Navigation components -->
{% macro breadcrumb(items, class="") %}
<nav class="flex {{ class }}" aria-label="Breadcrumb">
    <ol class="flex items-center space-x-2">
        {% for item in items %}
        <li class="flex items-center">
            {% if not loop.first %}
            <svg class="flex-shrink-0 h-4 w-4 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
            </svg>
            {% endif %}
            
            {% if item.url and not loop.last %}
            <a href="{{ item.url }}" 
               class="text-sm font-medium text-gray-500 hover:text-gray-700 focus:outline-none focus:underline">
                {{ item.name }}
            </a>
            {% else %}
            <span class="text-sm font-medium text-gray-900" 
                  {% if loop.last %}aria-current="page"{% endif %}>
                {{ item.name }}
            </span>
            {% endif %}
        </li>
        {% endfor %}
    </ol>
</nav>
{% endmacro %}

<!-- Pagination component -->
{% macro pagination(current_page, total_pages, base_url, class="") %}
{% if total_pages > 1 %}
<nav class="flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6 {{ class }}" 
     aria-label="Pagination">
    <div class="flex flex-1 justify-between sm:hidden">
        {% if current_page > 1 %}
        <a href="{{ base_url }}?page={{ current_page - 1 }}" 
           class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500">
            Previous
        </a>
        {% else %}
        <span class="relative inline-flex items-center rounded-md border border-gray-300 bg-gray-100 px-4 py-2 text-sm font-medium text-gray-400 cursor-not-allowed">
            Previous
        </span>
        {% endif %}
        
        {% if current_page < total_pages %}
        <a href="{{ base_url }}?page={{ current_page + 1 }}" 
           class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500">
            Next
        </a>
        {% else %}
        <span class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-gray-100 px-4 py-2 text-sm font-medium text-gray-400 cursor-not-allowed">
            Next
        </span>
        {% endif %}
    </div>
    
    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
        <div>
            <p class="text-sm text-gray-700">
                Showing page <span class="font-medium">{{ current_page }}</span> of 
                <span class="font-medium">{{ total_pages }}</span>
            </p>
        </div>
        
        <div>
            <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                <!-- Previous button -->
                {% if current_page > 1 %}
                <a href="{{ base_url }}?page={{ current_page - 1 }}" 
                   class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 focus:ring-2 focus:ring-primary-500"
                   aria-label="Go to previous page">
                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                    </svg>
                </a>
                {% else %}
                <span class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-300 ring-1 ring-inset ring-gray-300 cursor-not-allowed">
                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                    </svg>
                </span>
                {% endif %}
                
                <!-- Page numbers -->
                {% set start_page = [1, current_page - 2] | max %}
                {% set end_page = [total_pages, current_page + 2] | min %}
                
                {% if start_page > 1 %}
                <a href="{{ base_url }}?page=1" 
                   class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 focus:ring-2 focus:ring-primary-500">
                    1
                </a>
                {% if start_page > 2 %}
                <span class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300">...</span>
                {% endif %}
                {% endif %}
                
                {% for page in range(start_page, end_page + 1) %}
                {% if page == current_page %}
                <span class="relative z-10 inline-flex items-center bg-primary-600 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600"
                      aria-current="page">
                    {{ page }}
                </span>
                {% else %}
                <a href="{{ base_url }}?page={{ page }}" 
                   class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 focus:ring-2 focus:ring-primary-500">
                    {{ page }}
                </a>
                {% endif %}
                {% endfor %}
                
                {% if end_page < total_pages %}
                {% if end_page < total_pages - 1 %}
                <span class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300">...</span>
                {% endif %}
                <a href="{{ base_url }}?page={{ total_pages }}" 
                   class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 focus:ring-2 focus:ring-primary-500">
                    {{ total_pages }}
                </a>
                {% endif %}
                
                <!-- Next button -->
                {% if current_page < total_pages %}
                <a href="{{ base_url }}?page={{ current_page + 1 }}" 
                   class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 focus:ring-2 focus:ring-primary-500"
                   aria-label="Go to next page">
                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M7.21 5.23a.75.75 0 011.06.02L12.832 10l-4.562 4.75a.75.75 0 11-1.04-1.08L10.168 10 7.23 6.29a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                    </svg>
                </a>
                {% else %}
                <span class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-300 ring-1 ring-inset ring-gray-300 cursor-not-allowed">
                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M7.21 5.23a.75.75 0 011.06.02L12.832 10l-4.562 4.75a.75.75 0 11-1.04-1.08L10.168 10 7.23 6.29a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                    </svg>
                </span>
                {% endif %}
            </nav>
        </div>
    </div>
</nav>
{% endif %}
{% endmacro %}

<!-- Tab navigation component -->
{% macro tabs(items, current_tab, base_url="", class="") %}
<div class="{{ class }}">
    <div class="sm:hidden">
        <label for="tabs" class="sr-only">Select a tab</label>
        <select id="tabs" 
                name="tabs" 
                class="block w-full rounded-md border-gray-300 focus:border-primary-500 focus:ring-primary-500"
                onchange="window.location.href = this.value">
            {% for item in items %}
            <option value="{{ base_url }}{{ item.url }}" {% if item.key == current_tab %}selected{% endif %}>
                {{ item.name }}
            </option>
            {% endfor %}
        </select>
    </div>
    
    <div class="hidden sm:block">
        <nav class="flex space-x-8" aria-label="Tabs">
            {% for item in items %}
            <a href="{{ base_url }}{{ item.url }}" 
               class="{% if item.key == current_tab %}border-primary-500 text-primary-600{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{% endif %} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm focus:outline-none focus:text-primary-600 focus:border-primary-500"
               {% if item.key == current_tab %}aria-current="page"{% endif %}>
                {{ item.name }}
                {% if item.count is defined %}
                <span class="{% if item.key == current_tab %}bg-primary-100 text-primary-600{% else %}bg-gray-100 text-gray-900{% endif %} hidden ml-2 py-0.5 px-2.5 rounded-full text-xs font-medium md:inline-block">
                    {{ item.count }}
                </span>
                {% endif %}
            </a>
            {% endfor %}
        </nav>
    </div>
</div>
{% endmacro %}