<!-- Firebase-inspired form input components -->
{% macro input(name, label=None, type="text", value="", placeholder="", required=False, disabled=False, help_text=None, error=None, class="", id=None) %}
{% set input_id = id or name %}
<div class="firebase-form-group">
    {% if label %}
    <label for="{{ input_id }}" class="firebase-form-label">
        {{ label }}
        {% if required %}<span style="color: var(--firebase-red);" class="ml-1" aria-label="required">*</span>{% endif %}
    </label>
    {% endif %}
    
    <div class="relative">
        <input type="{{ type }}" 
               name="{{ name }}" 
               id="{{ input_id }}"
               value="{{ value }}"
               {% if placeholder %}placeholder="{{ placeholder }}"{% endif %}
               {% if required %}required aria-required="true"{% endif %}
               {% if disabled %}disabled{% endif %}
               {% if error %}aria-invalid="true" aria-describedby="{{ input_id }}-error"{% endif %}
               {% if help_text %}aria-describedby="{{ input_id }}-help"{% endif %}
               class="firebase-form-input {{ class }}"
               {% if error %}style="border-color: var(--firebase-red); box-shadow: 0 0 0 3px rgba(221, 44, 0, 0.1);"{% endif %}
               {% if disabled %}style="opacity: 0.5; cursor: not-allowed;"{% endif %}>
        
        {% if error %}
        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" style="color: var(--firebase-red);">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
            </svg>
        </div>
        {% endif %}
    </div>
    
    {% if help_text %}
    <p id="{{ input_id }}-help" class="text-sm" style="color: var(--text-secondary);">{{ help_text }}</p>
    {% endif %}
    
    {% if error %}
    <p id="{{ input_id }}-error" class="text-sm" role="alert" style="color: var(--firebase-red);">{{ error }}</p>
    {% endif %}
</div>
{% endmacro %}

<!-- Textarea component -->
{% macro textarea(name, label=None, value="", placeholder="", rows=3, required=False, disabled=False, help_text=None, error=None, class="", id=None) %}
{% set input_id = id or name %}
<div class="space-y-1">
    {% if label %}
    <label for="{{ input_id }}" class="block text-sm font-medium text-gray-700">
        {{ label }}
        {% if required %}<span class="text-red-500 ml-1" aria-label="required">*</span>{% endif %}
    </label>
    {% endif %}
    
    <div class="relative">
        <textarea name="{{ name }}" 
                  id="{{ input_id }}"
                  rows="{{ rows }}"
                  {% if placeholder %}placeholder="{{ placeholder }}"{% endif %}
                  {% if required %}required aria-required="true"{% endif %}
                  {% if disabled %}disabled{% endif %}
                  {% if error %}aria-invalid="true" aria-describedby="{{ input_id }}-error"{% endif %}
                  {% if help_text %}aria-describedby="{{ input_id }}-help"{% endif %}
                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm
                         {% if error %}border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500{% endif %}
                         {% if disabled %}bg-gray-50 text-gray-500{% endif %} {{ class }}">{{ value }}</textarea>
    </div>
    
    {% if help_text %}
    <p id="{{ input_id }}-help" class="text-sm text-gray-500">{{ help_text }}</p>
    {% endif %}
    
    {% if error %}
    <p id="{{ input_id }}-error" class="text-sm text-red-600" role="alert">{{ error }}</p>
    {% endif %}
</div>
{% endmacro %}

<!-- Select component -->
{% macro select(name, label=None, options=[], value="", required=False, disabled=False, help_text=None, error=None, class="", id=None) %}
{% set input_id = id or name %}
<div class="space-y-1">
    {% if label %}
    <label for="{{ input_id }}" class="block text-sm font-medium text-gray-700">
        {{ label }}
        {% if required %}<span class="text-red-500 ml-1" aria-label="required">*</span>{% endif %}
    </label>
    {% endif %}
    
    <select name="{{ name }}" 
            id="{{ input_id }}"
            {% if required %}required aria-required="true"{% endif %}
            {% if disabled %}disabled{% endif %}
            {% if error %}aria-invalid="true" aria-describedby="{{ input_id }}-error"{% endif %}
            {% if help_text %}aria-describedby="{{ input_id }}-help"{% endif %}
            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm
                   {% if error %}border-red-300 text-red-900 focus:border-red-500 focus:ring-red-500{% endif %}
                   {% if disabled %}bg-gray-50 text-gray-500{% endif %} {{ class }}">
        {% for option in options %}
        <option value="{{ option.value }}" {% if option.value == value %}selected{% endif %}>
            {{ option.label }}
        </option>
        {% endfor %}
    </select>
    
    {% if help_text %}
    <p id="{{ input_id }}-help" class="text-sm text-gray-500">{{ help_text }}</p>
    {% endif %}
    
    {% if error %}
    <p id="{{ input_id }}-error" class="text-sm text-red-600" role="alert">{{ error }}</p>
    {% endif %}
</div>
{% endmacro %}

<!-- Firebase-inspired button component -->
{% macro button(text, type="button", variant="primary", size="md", disabled=False, loading=False, icon=None, class="", hx_attrs={}) %}
{% set base_classes = "firebase-btn" %}
{% set size_classes = {
    "xs": "firebase-btn-sm text-xs",
    "sm": "firebase-btn-sm",
    "md": "",
    "lg": "firebase-btn-lg",
    "xl": "firebase-btn-lg text-lg"
} %}
{% set variant_classes = {
    "primary": "firebase-btn-primary",
    "secondary": "firebase-btn-secondary",
    "danger": "firebase-btn-danger",
    "success": "firebase-btn-primary",
    "ghost": "firebase-btn-secondary"
} %}

<button type="{{ type }}"
        {% if disabled or loading %}disabled{% endif %}
        {% if loading %}data-loading-text="{{ text }}..."{% endif %}
        class="{{ base_classes }} {{ size_classes[size] }} {{ variant_classes[variant] }} {{ class }}"
        {% for attr, value in hx_attrs.items() %}{{ attr }}="{{ value }}"{% endfor %}
        {% if disabled %}style="opacity: 0.5; cursor: not-allowed;"{% endif %}>
    
    {% if loading %}
    <svg class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    {% elif icon %}
    <span class="mr-2">{{ icon | safe }}</span>
    {% endif %}
    
    <span>{{ text }}</span>
</button>
{% endmacro %}