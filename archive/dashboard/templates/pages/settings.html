{% extends "base.html" %}

{% block title %}Settings - HTMX RAG Dashboard{% endblock %}
{% block page_title %}Settings{% endblock %}

{% block content %}
<div class="max-w-4xl">
    <!-- Server Status -->
    <div class="mb-6">
        {% if server_available %}
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800">MCP Server Connected</p>
                        <p class="text-sm text-green-700">Configuration changes will be applied immediately</p>
                    </div>
                </div>
            </div>
        {% else %}
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-yellow-800">MCP Server Unavailable</p>
                        <p class="text-sm text-yellow-700">Settings will be saved locally and applied when server reconnects</p>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Save Status Messages -->
    <div id="save-status" class="mb-6">
        {% if save_status == 'success' %}
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex items-center">
                    <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    <p class="ml-3 text-sm font-medium text-green-800">Settings saved successfully</p>
                </div>
            </div>
        {% elif save_status == 'error' %}
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex items-center">
                    <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                    </svg>
                    <p class="ml-3 text-sm font-medium text-red-800">Failed to save settings</p>
                </div>
            </div>
        {% endif %}
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Server Configuration -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Server Configuration</h3>
            <form id="server-settings-form" 
                  hx-post="/htmx/settings/update" 
                  hx-target="#save-status" 
                  hx-swap="innerHTML"
                  hx-indicator="#save-indicator"
                  class="space-y-6">
                
                <!-- RAG Strategy Settings -->
                <div>
                    <h4 class="text-md font-medium text-gray-700 mb-3">RAG Strategies</h4>
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="checkbox" 
                                   name="contextual_embeddings" 
                                   {% if current_settings.rag_strategies.contextual_embeddings %}checked{% endif %}
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-3 text-sm text-gray-700">
                                <span class="font-medium">Contextual Embeddings</span>
                                <span class="block text-xs text-gray-500">Enhanced semantic understanding with context</span>
                            </span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" 
                                   name="hybrid_search" 
                                   {% if current_settings.rag_strategies.hybrid_search %}checked{% endif %}
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-3 text-sm text-gray-700">
                                <span class="font-medium">Hybrid Search</span>
                                <span class="block text-xs text-gray-500">Combine semantic and keyword search</span>
                            </span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" 
                                   name="agentic_rag" 
                                   {% if current_settings.rag_strategies.agentic_rag %}checked{% endif %}
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-3 text-sm text-gray-700">
                                <span class="font-medium">Agentic RAG</span>
                                <span class="block text-xs text-gray-500">AI-powered query refinement and code extraction</span>
                            </span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" 
                                   name="reranking" 
                                   {% if current_settings.rag_strategies.reranking %}checked{% endif %}
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-3 text-sm text-gray-700">
                                <span class="font-medium">Reranking</span>
                                <span class="block text-xs text-gray-500">Improve result relevance with reranking models</span>
                            </span>
                        </label>
                    </div>
                </div>
                
                <!-- Crawling Parameters -->
                <div>
                    <h4 class="text-md font-medium text-gray-700 mb-3">Default Crawling Parameters</h4>
                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <label for="default_max_depth" class="block text-sm font-medium text-gray-700 mb-1">
                                Max Depth
                                <span class="text-xs text-gray-500">(1-10)</span>
                            </label>
                            <input type="number" 
                                   id="default_max_depth" 
                                   name="default_max_depth"
                                   value="{{ current_settings.crawling_parameters.default_max_depth }}" 
                                   min="1" max="10" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <p class="text-xs text-gray-500 mt-1">Maximum depth to crawl from starting URL</p>
                        </div>
                        <div>
                            <label for="default_max_concurrent" class="block text-sm font-medium text-gray-700 mb-1">
                                Max Concurrent
                                <span class="text-xs text-gray-500">(1-20)</span>
                            </label>
                            <input type="number" 
                                   id="default_max_concurrent" 
                                   name="default_max_concurrent"
                                   value="{{ current_settings.crawling_parameters.default_max_concurrent }}" 
                                   min="1" max="20" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <p class="text-xs text-gray-500 mt-1">Number of concurrent crawling operations</p>
                        </div>
                        <div>
                            <label for="default_chunk_size" class="block text-sm font-medium text-gray-700 mb-1">
                                Chunk Size
                                <span class="text-xs text-gray-500">(100-10000)</span>
                            </label>
                            <input type="number" 
                                   id="default_chunk_size" 
                                   name="default_chunk_size"
                                   value="{{ current_settings.crawling_parameters.default_chunk_size }}" 
                                   min="100" max="10000" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <p class="text-xs text-gray-500 mt-1">Size of text chunks for embedding storage</p>
                        </div>
                    </div>
                </div>
                
                <!-- Code Detection Settings -->
                <div>
                    <h4 class="text-md font-medium text-gray-700 mb-3">Code Detection Settings</h4>
                    <div class="space-y-4">
                        <label class="flex items-center">
                            <input type="checkbox" 
                                   name="code_detection_enabled" 
                                   {% if current_settings.code_detection.enabled %}checked{% endif %}
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-3 text-sm font-medium text-gray-700">Enable Code Detection</span>
                        </label>
                        <div>
                            <label for="min_code_length" class="block text-sm font-medium text-gray-700 mb-1">
                                Minimum Code Length
                                <span class="text-xs text-gray-500">(10-1000)</span>
                            </label>
                            <input type="number" 
                                   id="min_code_length" 
                                   name="min_code_length"
                                   value="{{ current_settings.code_detection.min_code_length }}" 
                                   min="10" max="1000" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <p class="text-xs text-gray-500 mt-1">Minimum characters to consider as code block</p>
                        </div>
                    </div>
                </div>
                
                <!-- Validation Errors -->
                {% if validation_errors %}
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h5 class="text-sm font-medium text-red-800 mb-2">Validation Errors:</h5>
                        <ul class="text-sm text-red-700 space-y-1">
                            {% for field, error in validation_errors.items() %}
                                <li>{{ field }}: {{ error }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}
                
                <div class="flex space-x-4">
                    <button type="submit" 
                            class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md font-medium flex items-center">
                        <span id="save-indicator" class="htmx-indicator mr-2">
                            <svg class="animate-spin h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </span>
                        Save Settings
                    </button>
                    <button type="button" 
                            hx-post="/htmx/settings/reset" 
                            hx-target="#save-status" 
                            hx-swap="innerHTML"
                            hx-confirm="Are you sure you want to reset all settings to defaults?"
                            class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md font-medium">
                        Reset to Defaults
                    </button>
                </div>
            </form>
        </div>

        <!-- Dashboard Settings -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Dashboard Settings</h3>
            <form id="dashboard-settings-form" 
                  hx-post="/htmx/settings/dashboard" 
                  hx-target="#save-status" 
                  hx-swap="innerHTML"
                  class="space-y-6">
                
                <div>
                    <label for="refresh_interval" class="block text-sm font-medium text-gray-700 mb-1">
                        Refresh Interval (ms)
                    </label>
                    <input type="number" 
                           id="refresh_interval" 
                           name="refresh_interval"
                           value="{{ dashboard_settings.refresh_interval }}" 
                           min="1000" max="60000" step="1000"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <p class="text-xs text-gray-500 mt-1">How often to refresh real-time data</p>
                </div>
                
                <div>
                    <label for="max_search_results" class="block text-sm font-medium text-gray-700 mb-1">
                        Max Search Results
                    </label>
                    <input type="number" 
                           id="max_search_results" 
                           name="max_search_results"
                           value="{{ dashboard_settings.max_search_results }}" 
                           min="10" max="200"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <p class="text-xs text-gray-500 mt-1">Maximum number of search results to display</p>
                </div>
                
                <div class="space-y-3">
                    <label class="flex items-center">
                        <input type="checkbox" 
                               name="enable_real_time" 
                               {% if dashboard_settings.enable_real_time %}checked{% endif %}
                               class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-3 text-sm text-gray-700">Enable Real-time Updates</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" 
                               name="auto_refresh_stats" 
                               {% if dashboard_settings.auto_refresh_stats %}checked{% endif %}
                               class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-3 text-sm text-gray-700">Auto-refresh Statistics</span>
                    </label>
                </div>
                
                <div>
                    <label for="theme" class="block text-sm font-medium text-gray-700 mb-1">Theme</label>
                    <select id="theme" 
                            name="theme"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="light" {% if dashboard_settings.theme == 'light' %}selected{% endif %}>Light</option>
                        <option value="dark" {% if dashboard_settings.theme == 'dark' %}selected{% endif %}>Dark</option>
                        <option value="auto" {% if dashboard_settings.theme == 'auto' %}selected{% endif %}>Auto</option>
                    </select>
                </div>
                
                <button type="submit" 
                        class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md font-medium">
                    Save Dashboard Settings
                </button>
            </form>
        </div>
    </div>

    <!-- Configuration Audit Log -->
    <div class="mt-6 bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Configuration Changes</h3>
        <div id="config-audit-log" 
             hx-get="/htmx/settings/audit-log" 
             hx-trigger="load, every 30s"
             class="space-y-2">
            <div class="text-sm text-gray-500">Loading audit log...</div>
        </div>
    </div>
</div>

<script>
// Auto-save form changes after a delay
document.addEventListener('DOMContentLoaded', function() {
    let saveTimeout;
    const forms = document.querySelectorAll('#server-settings-form input, #server-settings-form select');
    
    forms.forEach(input => {
        input.addEventListener('change', function() {
            clearTimeout(saveTimeout);
            saveTimeout = setTimeout(() => {
                // Auto-save after 2 seconds of no changes
                document.getElementById('server-settings-form').dispatchEvent(new Event('submit'));
            }, 2000);
        });
    });
});
</script>
{% endblock %}