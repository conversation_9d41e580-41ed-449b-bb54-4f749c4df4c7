{% extends "base.html" %}

{% block title %}RAG Query - HTMX RAG Dashboard{% endblock %}
{% block page_title %}RAG Query{% endblock %}

{% block content %}
<div class="max-w-6xl">
    <!-- Server Status Alert -->
    {% if not server_available %}
    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">RAG Server Unavailable</h3>
                <p class="text-sm text-red-700 mt-1">The RAG server is not available. Please check the server status and try again.</p>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- Search Interface -->
        <div class="lg:col-span-3">
            <!-- Main Search Form -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">Search Knowledge Base</h3>
                    {% if rag_strategies.agentic_rag %}
                    <div class="flex space-x-2">
                        <button id="general-search-tab" class="px-3 py-1 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md">General Search</button>
                        <button id="code-search-tab" class="px-3 py-1 text-sm font-medium text-gray-600 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100">Code Examples</button>
                    </div>
                    {% endif %}
                </div>
                
                <!-- General Search Form -->
                <form id="search-form" 
                      hx-post="/htmx/query/search" 
                      hx-target="#search-results" 
                      hx-indicator="#search-loading"
                      hx-trigger="submit"
                      class="space-y-4">
                    
                    <div class="relative">
                        <label for="query" class="block text-sm font-medium text-gray-700 mb-1">Query</label>
                        <input type="text" 
                               id="query" 
                               name="query" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                               placeholder="Enter your search query..."
                               hx-get="/htmx/query/suggestions"
                               hx-target="#search-suggestions"
                               hx-trigger="keyup changed delay:300ms"
                               autocomplete="off"
                               {% if not server_available %}disabled{% endif %}>
                        
                        <!-- Search Suggestions Dropdown -->
                        <div id="search-suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 hidden"></div>
                    </div>
                    
                    <!-- Advanced Filters -->
                    <div class="border-t pt-4">
                        <button type="button" 
                                id="toggle-filters" 
                                class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                            Show Advanced Filters
                        </button>
                        
                        <div id="advanced-filters" class="hidden mt-4 space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label for="max_results" class="block text-sm font-medium text-gray-700 mb-1">Max Results</label>
                                    <select id="max_results" name="max_results" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="10">10</option>
                                        <option value="25" selected>25</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="min_score" class="block text-sm font-medium text-gray-700 mb-1">Min Relevance Score</label>
                                    <select id="min_score" name="min_score" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="0.0" selected>Any</option>
                                        <option value="0.3">0.3+</option>
                                        <option value="0.5">0.5+</option>
                                        <option value="0.7">0.7+</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="source_filter" class="block text-sm font-medium text-gray-700 mb-1">Filter by Source</label>
                                    <select id="source_filter" name="source_filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">All Sources</option>
                                        {% for source in available_sources %}
                                        <option value="{{ source.id }}">{{ source.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <button type="submit" 
                                class="bg-green-500 hover:bg-green-600 text-white py-2 px-6 rounded-md font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                                {% if not server_available %}disabled{% endif %}>
                            Search
                        </button>
                        <button type="button" 
                                id="clear-search"
                                class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md font-medium">
                            Clear
                        </button>
                        <div id="search-loading" class="htmx-indicator">
                            <div class="flex items-center text-blue-600">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Searching...
                            </div>
                        </div>
                    </div>
                </form>
                
                <!-- Code Search Form (shown when agentic RAG is enabled) -->
                {% if rag_strategies.agentic_rag %}
                <form id="code-search-form" 
                      hx-post="/htmx/query/code-search" 
                      hx-target="#search-results" 
                      hx-indicator="#search-loading"
                      hx-trigger="submit"
                      class="space-y-4 hidden">
                    
                    <div>
                        <label for="code_query" class="block text-sm font-medium text-gray-700 mb-1">Code Search Query</label>
                        <input type="text" 
                               id="code_query" 
                               name="query" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                               placeholder="Search for code examples, functions, or patterns..."
                               {% if not server_available %}disabled{% endif %}>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="language_filter" class="block text-sm font-medium text-gray-700 mb-1">Programming Language</label>
                            <select id="language_filter" name="language_filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">All Languages</option>
                                <option value="python">Python</option>
                                <option value="javascript">JavaScript</option>
                                <option value="typescript">TypeScript</option>
                                <option value="java">Java</option>
                                <option value="cpp">C++</option>
                                <option value="go">Go</option>
                                <option value="rust">Rust</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="code_max_results" class="block text-sm font-medium text-gray-700 mb-1">Max Results</label>
                            <select id="code_max_results" name="max_results" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="10">10</option>
                                <option value="25" selected>25</option>
                                <option value="50">50</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <button type="submit" 
                                class="bg-green-500 hover:bg-green-600 text-white py-2 px-6 rounded-md font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                                {% if not server_available %}disabled{% endif %}>
                            Search Code
                        </button>
                        <button type="button" 
                                id="clear-code-search"
                                class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md font-medium">
                            Clear
                        </button>
                    </div>
                </form>
                {% endif %}
            </div>
            
            <!-- Search Results -->
            <div id="search-results" class="space-y-4">
                <!-- Search results will be loaded here via HTMX -->
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- Search History -->
            <div class="bg-white rounded-lg shadow p-4 mb-6">
                <h4 class="text-md font-semibold text-gray-800 mb-3">Recent Searches</h4>
                <div id="search-history" 
                     hx-get="/htmx/query/history" 
                     hx-trigger="load"
                     class="space-y-2">
                    <!-- Search history will be loaded here -->
                </div>
            </div>
            
            <!-- Saved Queries -->
            <div class="bg-white rounded-lg shadow p-4 mb-6">
                <h4 class="text-md font-semibold text-gray-800 mb-3">Saved Queries</h4>
                <div id="saved-queries" 
                     hx-get="/htmx/query/saved" 
                     hx-trigger="load"
                     class="space-y-2">
                    <!-- Saved queries will be loaded here -->
                </div>
            </div>
            
            <!-- Quick Stats -->
            <div class="bg-white rounded-lg shadow p-4">
                <h4 class="text-md font-semibold text-gray-800 mb-3">Search Stats</h4>
                <div id="search-stats" 
                     hx-get="/htmx/query/stats" 
                     hx-trigger="load"
                     class="space-y-2">
                    <!-- Search stats will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Toggle advanced filters
document.getElementById('toggle-filters').addEventListener('click', function() {
    const filters = document.getElementById('advanced-filters');
    const button = this;
    
    if (filters.classList.contains('hidden')) {
        filters.classList.remove('hidden');
        button.textContent = 'Hide Advanced Filters';
    } else {
        filters.classList.add('hidden');
        button.textContent = 'Show Advanced Filters';
    }
});

// Clear search form
document.getElementById('clear-search').addEventListener('click', function() {
    document.getElementById('search-form').reset();
    document.getElementById('search-results').innerHTML = '';
    document.getElementById('search-suggestions').classList.add('hidden');
});

{% if rag_strategies.agentic_rag %}
// Tab switching for general vs code search
document.getElementById('general-search-tab').addEventListener('click', function() {
    // Switch tabs
    this.classList.add('text-blue-600', 'bg-blue-50', 'border-blue-200');
    this.classList.remove('text-gray-600', 'bg-gray-50', 'border-gray-200');
    
    document.getElementById('code-search-tab').classList.remove('text-blue-600', 'bg-blue-50', 'border-blue-200');
    document.getElementById('code-search-tab').classList.add('text-gray-600', 'bg-gray-50', 'border-gray-200');
    
    // Switch forms
    document.getElementById('search-form').classList.remove('hidden');
    document.getElementById('code-search-form').classList.add('hidden');
    
    // Clear results
    document.getElementById('search-results').innerHTML = '';
});

document.getElementById('code-search-tab').addEventListener('click', function() {
    // Switch tabs
    this.classList.add('text-blue-600', 'bg-blue-50', 'border-blue-200');
    this.classList.remove('text-gray-600', 'bg-gray-50', 'border-gray-200');
    
    document.getElementById('general-search-tab').classList.remove('text-blue-600', 'bg-blue-50', 'border-blue-200');
    document.getElementById('general-search-tab').classList.add('text-gray-600', 'bg-gray-50', 'border-gray-200');
    
    // Switch forms
    document.getElementById('code-search-form').classList.remove('hidden');
    document.getElementById('search-form').classList.add('hidden');
    
    // Clear results
    document.getElementById('search-results').innerHTML = '';
});

// Clear code search form
document.getElementById('clear-code-search').addEventListener('click', function() {
    document.getElementById('code-search-form').reset();
    document.getElementById('search-results').innerHTML = '';
});
{% endif %}

// Hide search suggestions when clicking outside
document.addEventListener('click', function(event) {
    const suggestions = document.getElementById('search-suggestions');
    const queryInput = document.getElementById('query');
    
    if (!suggestions.contains(event.target) && event.target !== queryInput) {
        suggestions.classList.add('hidden');
    }
});
</script>
{% endblock %}