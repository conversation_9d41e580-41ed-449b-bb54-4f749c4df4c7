{% extends "base.html" %}
{% from "components/card.html" import card %}

{% block title %}Rate Limiting Administration - HTMX RAG Dashboard{% endblock %}
{% block page_title %}Rate Limiting Administration{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto space-y-6">
    <!-- Statistics Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="firebase-metric-card">
            <div class="firebase-metric-value">{{ rules|length }}</div>
            <div class="firebase-metric-label">Active Rules</div>
        </div>
        <div class="firebase-metric-card">
            <div class="firebase-metric-value">{{ total_blocked }}</div>
            <div class="firebase-metric-label">Blocked IPs</div>
        </div>
        <div class="firebase-metric-card">
            <div class="firebase-metric-value">{{ total_monitored }}</div>
            <div class="firebase-metric-label">Monitored IPs</div>
        </div>
        <div class="firebase-metric-card">
            <div class="firebase-metric-value" style="color: var(--android-green) !important;">Online</div>
            <div class="firebase-metric-label">Rate Limiter Status</div>
        </div>
    </div>

    <!-- IP Management Section -->
    {% call card(title="IP Address Management", icon='<svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>') %}
        <div class="space-y-4">
            <!-- IP Lookup Form -->
            <form class="flex gap-2" 
                  hx-get="/admin/rate-limits/status" 
                  hx-target="#ip-status-result"
                  hx-include="[name='ip']">
                <input type="text" 
                       name="ip" 
                       placeholder="Enter IP address (e.g., ***********)" 
                       class="flex-1 form-input"
                       pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
                       required>
                <button type="submit" class="firebase-btn firebase-btn-primary">Check Status</button>
            </form>
            
            <!-- IP Status Result -->
            <div id="ip-status-result" class="mt-4"></div>
        </div>
    {% endcall %}

    <!-- Blocked IPs Section -->
    {% if blocked_ips %}
    {% call card(title="Currently Blocked IPs", icon='<svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636"></path></svg>') %}
        <div class="space-y-2">
            {% for ip in blocked_ips %}
            <div class="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-md">
                <div>
                    <span class="font-medium text-red-800">{{ ip }}</span>
                    <span class="text-sm text-red-600 ml-2">
                        {% if abuse_scores.get(ip) %}
                            (Abuse score: {{ "%.1f"|format(abuse_scores[ip]) }})
                        {% endif %}
                    </span>
                </div>
                <div class="flex gap-2">
                    <button class="firebase-btn firebase-btn-secondary btn-sm"
                            hx-post="/admin/rate-limits/unblock/{{ ip }}"
                            hx-target="#admin-messages"
                            hx-swap="innerHTML"
                            onclick="confirm('Unblock IP {{ ip }}?') || event.stopImmediatePropagation()">
                        Unblock
                    </button>
                    <button class="firebase-btn firebase-btn-secondary btn-sm"
                            hx-post="/admin/rate-limits/reset-abuse/{{ ip }}"
                            hx-target="#admin-messages"
                            hx-swap="innerHTML">
                        Reset Score
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
    {% endcall %}
    {% endif %}

    <!-- Abuse Scores Section -->
    {% if abuse_scores %}
    {% call card(title="IP Abuse Scores", icon='<svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path></svg>') %}
        <div class="space-y-2">
            {% for ip, score in abuse_scores.items() %}
            {% if score > 0 %}
            <div class="flex items-center justify-between p-3 
                        {% if score >= 5 %}bg-red-50 border-red-200{% elif score >= 2 %}bg-yellow-50 border-yellow-200{% else %}bg-blue-50 border-blue-200{% endif %} 
                        border rounded-md">
                <div>
                    <span class="font-medium 
                                {% if score >= 5 %}text-red-800{% elif score >= 2 %}text-yellow-800{% else %}text-blue-800{% endif %}">
                        {{ ip }}
                    </span>
                    <span class="text-sm 
                                {% if score >= 5 %}text-red-600{% elif score >= 2 %}text-yellow-600{% else %}text-blue-600{% endif %} 
                                ml-2">
                        Score: {{ "%.1f"|format(score) }}
                    </span>
                </div>
                <button class="firebase-btn firebase-btn-secondary btn-sm"
                        hx-post="/admin/rate-limits/reset-abuse/{{ ip }}"
                        hx-target="#admin-messages"
                        hx-swap="innerHTML">
                    Reset Score
                </button>
            </div>
            {% endif %}
            {% endfor %}
        </div>
    {% endcall %}
    {% endif %}

    <!-- Rate Limiting Rules Section -->
    {% call card(title="Rate Limiting Rules", icon='<svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>') %}
        <div class="overflow-x-auto">
            <table class="table w-full">
                <thead>
                    <tr>
                        <th>Requests</th>
                        <th>Window (seconds)</th>
                        <th>Endpoint</th>
                        <th>Method</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    {% for rule in rules %}
                    <tr>
                        <td class="font-medium">{{ rule.requests }}</td>
                        <td>{{ rule.window }}</td>
                        <td>
                            <code class="px-2 py-1 bg-gray-100 rounded text-sm">{{ rule.endpoint }}</code>
                        </td>
                        <td>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                         {% if rule.method == 'GET' %}bg-green-100 text-green-800{% elif rule.method == 'POST' %}bg-blue-100 text-blue-800{% elif rule.method == '*' %}bg-gray-100 text-gray-800{% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                {{ rule.method }}
                            </span>
                        </td>
                        <td>{{ rule.description }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% endcall %}

    <!-- Admin Messages Area -->
    <div id="admin-messages"></div>
</div>

<script>
// Auto-refresh statistics every 30 seconds
setInterval(function() {
    if (document.visibilityState === 'visible') {
        htmx.trigger(document.body, 'refresh-stats');
    }
}, 30000);

// Handle form submissions with better UX
document.addEventListener('htmx:configRequest', function(evt) {
    if (evt.detail.path.includes('/admin/rate-limits/status/')) {
        const form = evt.target.closest('form');
        const ipInput = form.querySelector('input[name="ip"]');
        if (ipInput && ipInput.value) {
            evt.detail.path = `/admin/rate-limits/status/${ipInput.value}`;
        }
    }
});

// Handle successful responses
document.addEventListener('htmx:afterSwap', function(evt) {
    if (evt.target.id === 'admin-messages') {
        // Auto-hide success messages after 5 seconds
        setTimeout(function() {
            const messages = document.querySelectorAll('#admin-messages .alert-success');
            messages.forEach(msg => msg.remove());
        }, 5000);
    }
});

// Handle IP status responses
document.addEventListener('htmx:afterSwap', function(evt) {
    if (evt.target.id === 'ip-status-result') {
        try {
            const response = JSON.parse(evt.detail.xhr.responseText);
            let html = '<div class="p-4 border rounded-md ';
            
            if (response.blocked) {
                html += 'bg-red-50 border-red-200">';
                html += '<h4 class="font-medium text-red-800">IP Status: BLOCKED</h4>';
                if (response.block_expires_in) {
                    html += `<p class="text-red-600 text-sm">Block expires in ${response.block_expires_in} seconds</p>`;
                }
            } else {
                html += 'bg-green-50 border-green-200">';
                html += '<h4 class="font-medium text-green-800">IP Status: ALLOWED</h4>';
            }
            
            if (response.abuse_score > 0) {
                html += `<p class="text-sm mt-2">Abuse Score: ${response.abuse_score.toFixed(1)}</p>`;
            }
            
            html += '</div>';
            evt.target.innerHTML = html;
        } catch (e) {
            evt.target.innerHTML = '<div class="p-4 bg-red-50 border border-red-200 rounded-md"><p class="text-red-800">Error checking IP status</p></div>';
        }
    }
});
</script>
{% endblock %}