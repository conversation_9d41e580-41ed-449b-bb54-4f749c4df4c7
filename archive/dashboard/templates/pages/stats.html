{% extends "base.html" %}

{% block title %}Statistics - HTMX RAG Dashboard{% endblock %}
{% block page_title %}Statistics & Analytics{% endblock %}

{% block head %}
<!-- Chart.js for data visualization -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <!-- Auto-refresh toggle -->
    <div class="flex items-center" x-data="{ autoRefresh: true }">
        <label class="flex items-center cursor-pointer">
            <input type="checkbox" 
                   x-model="autoRefresh" 
                   class="sr-only"
                   @change="autoRefresh ? startAutoRefresh() : stopAutoRefresh()">
            <div class="relative">
                <div class="block bg-gray-600 w-14 h-8 rounded-full" :class="{'bg-green-600': autoRefresh}"></div>
                <div class="dot absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition" :class="{'transform translate-x-6': autoRefresh}"></div>
            </div>
            <span class="ml-3 text-sm font-medium text-gray-700">Auto-refresh</span>
        </label>
    </div>
    
    <!-- Manual refresh button -->
    <button hx-get="/stats/htmx/overview-metrics" 
            hx-target="#overview-metrics"
            hx-trigger="click"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        Refresh
    </button>
    
    <!-- Last updated indicator -->
    <div class="text-sm text-gray-500">
        Last updated: <span id="last-updated">{{ last_updated.strftime('%H:%M:%S') if last_updated else 'Never' }}</span>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="space-y-8">
    <!-- Server Status Alert -->
    {% if not server_available %}
    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">MCP Server Unavailable</h3>
                <p class="mt-1 text-sm text-yellow-700">Statistics may be limited or outdated. Please check the server connection.</p>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Overview Metrics -->
    <div id="overview-metrics" 
         hx-get="/stats/htmx/overview-metrics" 
         hx-trigger="load, every 30s"
         hx-swap="innerHTML">
        {% include "partials/overview_metrics.html" %}
    </div>

    <!-- Charts Overview with Historical Data -->
    {% if server_available %}
    <div id="charts-overview" 
         hx-get="/stats/htmx/charts-overview" 
         hx-trigger="load, every 60s"
         hx-swap="innerHTML">
        {% include "partials/charts_overview.html" %}
    </div>
    {% endif %}

    <!-- Content Breakdown and Performance Side by Side -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Content Breakdown -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Content Breakdown</h3>
                <p class="text-sm text-gray-600">Distribution of content across sources and types</p>
            </div>
            <div id="content-breakdown" 
                 hx-get="/stats/htmx/content-breakdown" 
                 hx-trigger="load, every 60s"
                 hx-swap="innerHTML"
                 class="p-6">
                {% include "partials/content_breakdown.html" %}
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Performance Metrics</h3>
                <p class="text-sm text-gray-600">System performance and response times</p>
            </div>
            <div id="performance-metrics" 
                 hx-get="/stats/htmx/performance-metrics" 
                 hx-trigger="load, every 30s"
                 hx-swap="innerHTML"
                 class="p-6">
                {% include "partials/performance_metrics.html" %}
            </div>
        </div>
    </div>

    <!-- RAG Strategies and Error Statistics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- RAG Strategy Usage -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">RAG Strategy Usage</h3>
                <p class="text-sm text-gray-600">Usage statistics for different RAG strategies</p>
            </div>
            <div id="rag-strategies" 
                 hx-get="/stats/htmx/rag-strategies" 
                 hx-trigger="load, every 60s"
                 hx-swap="innerHTML"
                 class="p-6">
                {% include "partials/rag_strategies.html" %}
            </div>
        </div>

        <!-- Error Statistics -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Error Statistics</h3>
                <p class="text-sm text-gray-600">System errors and failure rates</p>
            </div>
            <div id="error-statistics" 
                 hx-get="/stats/htmx/error-statistics" 
                 hx-trigger="load, every 60s"
                 hx-swap="innerHTML"
                 class="p-6">
                {% include "partials/error_statistics.html" %}
            </div>
        </div>
    </div>

    <!-- Source Analysis Section -->
    {% if server_available %}
    <div id="source-analysis" 
         hx-get="/stats/htmx/source-analysis" 
         hx-trigger="load, every 120s"
         hx-swap="innerHTML">
        {% include "partials/source_analysis.html" %}
    </div>
    {% endif %}

    <!-- Code Metrics Section (when Agentic RAG is enabled) -->
    {% if server_available and (rag_strategy_usage.agentic_rag.enabled or overview_metrics.code_examples.value > 0) %}
    <div id="code-metrics" 
         hx-get="/stats/htmx/code-metrics" 
         hx-trigger="load, every 120s"
         hx-swap="innerHTML">
        {% include "partials/code_metrics.html" %}
    </div>
    {% endif %}

    <!-- Activity Timeline -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
            <p class="text-sm text-gray-600">Timeline of recent system activities and operations</p>
        </div>
        <div id="activity-timeline" 
             hx-get="/stats/htmx/activity-timeline" 
             hx-trigger="load, every 30s"
             hx-swap="innerHTML"
             class="p-6">
            {% include "partials/activity_timeline.html" %}
        </div>
    </div>
</div>

<script>
let autoRefreshInterval;

function startAutoRefresh() {
    if (autoRefreshInterval) clearInterval(autoRefreshInterval);
    
    autoRefreshInterval = setInterval(() => {
        // Trigger refresh for all auto-updating sections
        htmx.trigger('#overview-metrics', 'refresh');
        htmx.trigger('#performance-metrics', 'refresh');
        htmx.trigger('#activity-timeline', 'refresh');
        
        // Update last updated time
        document.getElementById('last-updated').textContent = new Date().toLocaleTimeString();
    }, 30000); // 30 seconds
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }
}

// Start auto-refresh by default
document.addEventListener('DOMContentLoaded', function() {
    startAutoRefresh();
});

// Stop auto-refresh when page is not visible
document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
        startAutoRefresh();
    } else {
        stopAutoRefresh();
    }
});
</script>
{% endblock %}