{% extends "base.html" %}
{% from "components/form.html" import input, button, select %}
{% from "components/card.html" import card, metric_card %}

{% block title %}Web Crawling - HTMX RAG Dashboard{% endblock %}
{% block page_title %}Web Crawling{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto space-y-6">
    <!-- Server Status Alert -->
    {% if not server_available %}
    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">MCP Server Unavailable</h3>
                <p class="mt-1 text-sm text-yellow-700">The RAG server is not available. Please check if the server is running before starting crawl operations.</p>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Crawl Form -->
    {% call card(title="Start Web Crawl", icon='<svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0 9c0 5-4 9-9 9s-9-4-9-9m9 9c0-5 4-9 9-9s9 4 9 9"></path></svg>') %}
        <form id="crawl-form" 
              hx-post="/htmx/crawl/start" 
              hx-target="#crawl-results" 
              hx-swap="innerHTML"
              hx-indicator="#crawl-loading"
              class="validate-form space-y-6">
            
            <!-- URL Input -->
            {{ input(
                name="url",
                label="Website URL",
                type="url",
                placeholder="https://example.com",
                required=True,
                help_text="Enter the URL you want to crawl. The crawler will start from this page and follow links.",
                class="text-lg"
            ) }}
            
            <!-- Advanced Options Toggle -->
            <div class="border-t pt-4">
                <button type="button" 
                        onclick="document.getElementById('advanced-options').classList.toggle('hidden')"
                        class="flex items-center text-sm font-medium text-gray-700 hover:text-gray-900">
                    <svg class="w-4 h-4 mr-2 transform transition-transform" id="advanced-toggle-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                    Advanced Options
                </button>
                
                <div id="advanced-options" class="hidden mt-4 space-y-4 bg-gray-50 p-4 rounded-md">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="max_depth" class="block text-sm font-medium text-gray-700 mb-1">Max Depth</label>
                            <input type="number" id="max_depth" name="max_depth" value="{{ default_options.max_depth }}" min="1" max="10" 
                                   class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm">
                            <p class="text-sm text-gray-500 mt-1">Maximum depth to crawl (1-10)</p>
                        </div>
                        
                        <div>
                            <label for="max_concurrent" class="block text-sm font-medium text-gray-700 mb-1">Max Concurrent</label>
                            <input type="number" id="max_concurrent" name="max_concurrent" value="{{ default_options.max_concurrent }}" min="1" max="20" 
                                   class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm">
                            <p class="text-sm text-gray-500 mt-1">Maximum concurrent requests (1-20)</p>
                        </div>
                        
                        <div>
                            <label for="chunk_size" class="block text-sm font-medium text-gray-700 mb-1">Chunk Size</label>
                            <input type="number" id="chunk_size" name="chunk_size" value="{{ default_options.chunk_size }}" min="500" max="10000" 
                                   class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm">
                            <p class="text-sm text-gray-500 mt-1">Text chunk size for processing</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" 
                               id="force_direct" 
                               name="force_direct" 
                               class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                        <label for="force_direct" class="ml-2 block text-sm text-gray-700">
                            Force direct crawling (bypass smart detection)
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- Submit Button -->
            <div class="flex justify-end">
                {{ button(
                    text="Start Crawl",
                    type="submit",
                    variant="primary",
                    size="lg",
                    disabled=not server_available,
                    icon='<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path></svg>'
                ) }}
            </div>
        </form>
        
        <!-- Loading Indicator -->
        <div id="crawl-loading" class="htmx-indicator mt-4">
            <div class="flex items-center justify-center p-4 bg-blue-50 rounded-md">
                <svg class="animate-spin h-5 w-5 text-blue-600 mr-3" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-blue-700 font-medium">Starting crawl operation...</span>
            </div>
        </div>
    {% endcall %}
    
    <!-- Crawl Results -->
    <div id="crawl-results">
        <!-- Results will be loaded here via HTMX -->
    </div>
    
    <!-- Active Crawls -->
    {% if active_crawls %}
    {% call card(title="Active Crawls", icon='<svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg>') %}
        <div class="space-y-3">
            {% for crawl in active_crawls %}
            <div class="flex items-center justify-between p-3 bg-orange-50 rounded-md">
                <div class="flex-1">
                    <p class="font-medium text-gray-900">{{ crawl.url }}</p>
                    <p class="text-sm text-gray-600">Started {{ crawl.start_time }}</p>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="w-24 bg-gray-200 rounded-full h-2">
                        <div class="bg-orange-600 h-2 rounded-full" style="width: {{ crawl.progress }}%"></div>
                    </div>
                    <span class="text-sm font-medium text-orange-600">{{ crawl.progress }}%</span>
                </div>
            </div>
            {% endfor %}
        </div>
    {% endcall %}
    {% endif %}
    
    <!-- Crawl History -->
    {% if crawl_history %}
    {% call card(title="Recent Crawls", icon='<svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>') %}
        <div class="space-y-3">
            {% for crawl in crawl_history %}
            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                <div class="flex-1">
                    <p class="font-medium text-gray-900">{{ crawl.url }}</p>
                    <p class="text-sm text-gray-600">{{ crawl.timestamp }}</p>
                </div>
                <div class="flex items-center space-x-3">
                    {% if crawl.success %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Success
                    </span>
                    <span class="text-sm text-gray-600">{{ crawl.chunks_stored }} chunks</span>
                    {% else %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                        Failed
                    </span>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    {% endcall %}
    {% endif %}
</div>

<script>
// Toggle advanced options icon rotation
document.addEventListener('DOMContentLoaded', function() {
    const toggleButton = document.querySelector('[onclick*="advanced-options"]');
    const advancedOptions = document.getElementById('advanced-options');
    const toggleIcon = document.getElementById('advanced-toggle-icon');
    
    if (toggleButton) {
        toggleButton.addEventListener('click', function() {
            setTimeout(() => {
                if (advancedOptions.classList.contains('hidden')) {
                    toggleIcon.style.transform = 'rotate(0deg)';
                } else {
                    toggleIcon.style.transform = 'rotate(180deg)';
                }
            }, 10);
        });
    }
});
</script>
{% endblock %}