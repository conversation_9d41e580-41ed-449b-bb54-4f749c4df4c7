{% extends "base.html" %}

{% block title %}Content Management - HTMX RAG Dashboard{% endblock %}
{% block page_title %}Content Management{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto space-y-6">
    <!-- Server Status Alert -->
    {% if not server_available %}
    <div class="firebase-alert firebase-alert-warning">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5" style="color: var(--firebase-yellow) !important;" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium" style="color: var(--firebase-yellow) !important;">MCP Server Unavailable</h3>
                <p class="mt-1 text-sm" style="color: var(--text-secondary) !important;">Cannot manage content while the RAG server is offline.</p>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Overview Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="firebase-metric-card">
            <h3 class="text-lg font-semibold mb-2" style="color: var(--text-primary) !important;">Total Sources</h3>
            <p class="text-3xl font-bold" style="color: var(--firebase-blue) !important;">{{ total_sources }}</p>
        </div>
        
        <div class="firebase-metric-card">
            <h3 class="text-lg font-semibold mb-2" style="color: var(--text-primary) !important;">Total Documents</h3>
            <p class="text-3xl font-bold" style="color: var(--android-green) !important;">{{ total_documents }}</p>
        </div>
        
        <div class="firebase-metric-card">
            <h3 class="text-lg font-semibold mb-2" style="color: var(--text-primary) !important;">Server Status</h3>
            <p class="text-lg font-semibold" style="color: {% if server_available %}var(--android-green){% else %}var(--firebase-red){% endif %} !important;">
                {% if server_available %}Online{% else %}Offline{% endif %}
            </p>
        </div>
    </div>

    <!-- Sources List -->
    <div class="firebase-metric-card">
        <div class="px-6 py-4" style="border-bottom: 1px solid var(--surface-dark) !important;">
            <h3 class="text-lg font-medium" style="color: var(--text-primary) !important;">Crawled Sources</h3>
            <p class="text-sm" style="color: var(--text-secondary) !important;">Manage your crawled content sources</p>
        </div>
        
        <div class="p-6">
            {% if sources %}
            <div class="space-y-4">
                {% for source in sources %}
                <div class="rounded-lg p-4 firebase-hover-card" style="border: 1px solid var(--surface-dark) !important;">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <h4 class="text-lg font-medium" style="color: var(--text-primary) !important;">{{ source.name }}</h4>
                            {% if source.url %}
                            <a href="{{ source.url }}" target="_blank" class="text-sm" style="color: var(--firebase-blue) !important;">
                                {{ source.url }}
                            </a>
                            {% endif %}
                            
                            <div class="mt-2 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm" style="color: var(--text-secondary) !important;">
                                <div>
                                    <span class="font-medium">Documents:</span>
                                    <span class="ml-1">{{ source.document_count }}</span>
                                </div>
                                {% if source.last_crawl %}
                                <div>
                                    <span class="font-medium">Last Crawl:</span>
                                    <span class="ml-1">{{ source.last_crawl }}</span>
                                </div>
                                {% endif %}
                                {% if source.avg_size %}
                                <div>
                                    <span class="font-medium">Avg Size:</span>
                                    <span class="ml-1">{{ source.avg_size }} chars</span>
                                </div>
                                {% endif %}
                                {% if source.success_rate %}
                                <div>
                                    <span class="font-medium">Success Rate:</span>
                                    <span class="ml-1">{{ "%.1f"|format(source.success_rate * 100) }}%</span>
                                </div>
                                {% endif %}
                            </div>
                            
                            {% if source.summary %}
                            <p class="mt-2 text-sm text-gray-700">{{ source.summary }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Actions -->
                        <div class="flex items-center space-x-2 ml-4">
                            <button class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm"
                                    hx-post="/content/htmx/source/{{ source.name | urlencode }}/refresh"
                                    hx-target="#refresh-result-{{ loop.index0 }}"
                                    hx-swap="innerHTML"
                                    {% if not server_available %}disabled{% endif %}>
                                Refresh
                            </button>
                            
                            <button class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm"
                                    hx-delete="/content/htmx/source/{{ source.name | urlencode }}"
                                    hx-target="#delete-result-{{ loop.index0 }}"
                                    hx-swap="innerHTML"
                                    hx-confirm="Are you sure you want to delete '{{ source.name }}' and all its documents?"
                                    {% if not server_available %}disabled{% endif %}>
                                Delete
                            </button>
                        </div>
                    </div>
                    
                    <!-- Action Results -->
                    <div id="refresh-result-{{ loop.index0 }}" class="mt-3"></div>
                    <div id="delete-result-{{ loop.index0 }}" class="mt-3"></div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No sources found</h3>
                <p class="mt-1 text-sm text-gray-500">
                    {% if server_available %}
                        Start by crawling some websites to see them here.
                    {% else %}
                        Server is offline - cannot load sources.
                    {% endif %}
                </p>
                {% if server_available %}
                <div class="mt-6">
                    <a href="/crawl" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
                        Start Crawling
                    </a>
                </div>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}