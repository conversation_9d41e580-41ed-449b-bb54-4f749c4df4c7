{% extends "base.html" %}

{% block title %}Dashboard - HTMX RAG Dashboard{% endblock %}
{% block page_title %}Dashboard{% endblock %}

{% block content %}
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-[1fr_3fr_1fr] gap-6">
    <!-- Overview Cards with Real-time Updates -->
    <div class="firebase-metric-card">
        <div class="flex items-center justify-between mb-2">
            <h3 class="text-lg font-semibold" style="color: var(--text-primary) !important;">Total Documents</h3>
            <svg class="w-5 h-5" style="color: var(--google-blue);" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
        </div>
        <div class="firebase-metric-value">
            {{ quick_stats.total_documents | default(0) }}
        </div>
        <p class="firebase-metric-label">Crawled and indexed</p>
    </div>
    
    <div class="firebase-metric-card">
        <div class="flex items-center justify-between mb-2">
            <h3 class="text-lg font-semibold" style="color: var(--text-primary) !important;">Sources</h3>
            <svg class="w-5 h-5" style="color: var(--android-green);" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
            </svg>
        </div>
        <div class="firebase-metric-value">
            {{ quick_stats.total_sources | default(0) }}
        </div>
        <p class="firebase-metric-label">Unique domains</p>
    </div>
    
    <div class="firebase-metric-card">
        <div class="flex items-center justify-between mb-2">
            <h3 class="text-lg font-semibold" style="color: var(--text-primary) !important;">System Status</h3>
            <svg class="w-5 h-5" :style="'color: ' + (systemStatus === 'healthy' ? 'var(--android-green)' : systemStatus === 'warning' ? 'var(--firebase-yellow)' : 'var(--firebase-red)')" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true" x-data="{ systemStatus: '{{ system_status }}' }">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
            </svg>
        </div>
        <div class="text-lg font-semibold" :style="'color: ' + (systemStatus === 'healthy' ? 'var(--android-green)' : systemStatus === 'warning' ? 'var(--firebase-yellow)' : 'var(--firebase-red)') + ' !important'" x-data="{ systemStatus: '{{ system_status }}' }">
            {% if system_status == 'healthy' %}
                Online
            {% elif system_status == 'warning' %}
                Starting
            {% elif system_status == 'disconnected' %}
                Offline
            {% else %}
                Error
            {% endif %}
        </div>
        <p class="firebase-metric-label">MCP server status</p>
    </div>
</div>

<!-- Quick Actions -->
<div class="mt-8">
    <h3 class="text-xl font-semibold mb-4" style="color: var(--text-primary) !important;">Quick Actions</h3>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <a href="/crawl" class="firebase-btn firebase-btn-secondary px-6 py-3 rounded-lg text-center transition-colors duration-200 flex items-center justify-center" style="background-color: var(--firebase-orange) !important; color: white !important;">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
            </svg>
            Start Crawl
        </a>
        <a href="/query" class="firebase-btn firebase-btn-secondary px-6 py-3 rounded-lg text-center transition-colors duration-200 flex items-center justify-center" style="background-color: var(--firebase-orange) !important; color: white !important;">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            Search
        </a>
        <a href="/content" class="firebase-btn firebase-btn-secondary px-6 py-3 rounded-lg text-center transition-colors duration-200 flex items-center justify-center" style="background-color: var(--firebase-orange) !important; color: white !important;">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Manage Content
        </a>
        <a href="/stats" class="firebase-btn firebase-btn-secondary px-6 py-3 rounded-lg text-center transition-colors duration-200 flex items-center justify-center" style="background-color: var(--firebase-orange) !important; color: white !important;">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            View Stats
        </a>
    </div>
</div>

<!-- Recent Activity -->
{% if recent_activity %}
<div class="mt-8">
    <h3 class="text-xl font-semibold mb-4" style="color: var(--text-primary) !important;">Recent Activity</h3>
    <div class="firebase-metric-card">
        <div class="space-y-4">
            {% for activity in recent_activity %}
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    {% if activity.type == 'crawl' %}
                        <div class="w-8 h-8 rounded-full flex items-center justify-center" style="background-color: var(--firebase-blue-light) !important;">
                            <svg class="w-4 h-4" style="color: var(--firebase-blue) !important;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                            </svg>
                        </div>
                    {% elif activity.type == 'query' %}
                        <div class="w-8 h-8 rounded-full flex items-center justify-center" style="background-color: var(--android-green-light) !important;">
                            <svg class="w-4 h-4" style="color: var(--android-green) !important;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    {% else %}
                        <div class="w-8 h-8 rounded-full flex items-center justify-center" style="background-color: var(--surface-dark) !important;">
                            <svg class="w-4 h-4" style="color: var(--text-secondary) !important;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                    {% endif %}
                </div>
                <div class="ml-4 flex-1">
                    <p class="text-sm font-medium" style="color: var(--text-primary) !important;">{{ activity.title }}</p>
                    <p class="text-sm" style="color: var(--text-secondary) !important;">{{ activity.description }}</p>
                    <p class="text-xs mt-1" style="color: var(--text-muted) !important;">{{ activity.timestamp }}</p>
                </div>
                <div class="ml-4 flex-shrink-0">
                    <span class="firebase-status-chip {% if activity.status == 'success' %}firebase-status-success{% elif activity.status == 'warning' %}firebase-status-warning{% else %}firebase-status-error{% endif %}">
                        {{ activity.status | title }}
                    </span>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-4 text-center">
            <a href="/stats" class="text-sm font-medium" style="color: var(--firebase-orange) !important;">View all activity →</a>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}