<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="HTMX RAG Dashboard - Web interface for managing RAG operations, crawling, and queries">
    <title>{% block title %}{{ title | default('HTMX RAG Dashboard') }}{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    
    <!-- Alpine.js for minimal client-side state -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', path='css/dashboard.css') }}">
    
    <!-- Form Validation -->
    <script src="{{ url_for('static', path='js/form-validation.js') }}" defer></script>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔍</text></svg>">
    
    {% block head %}{% endblock %}
</head>
<body class="h-full antialiased" x-data="{ sidebarOpen: false, theme: 'dark' }" x-init="
    // Initialize theme from localStorage or system preference
    theme = localStorage.getItem('theme') || 'dark';
    $watch('theme', value => {
        localStorage.setItem('theme', value);
        document.documentElement.setAttribute('data-theme', value);
    });
    // Set initial theme attribute
    document.documentElement.setAttribute('data-theme', theme);
" style="background-color: var(--deep-dark) !important; color: var(--text-primary) !important;">
    <!-- Skip to main content link for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-600 text-white px-4 py-2 rounded-md z-50">
        Skip to main content
    </a>

    <!-- Mobile sidebar overlay -->
    <div x-show="sidebarOpen" 
         x-transition:enter="transition-opacity ease-linear duration-300" 
         x-transition:enter-start="opacity-0" 
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100" 
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-40 lg:hidden"
         aria-hidden="true">
        <div class="fixed inset-0 bg-gray-600 bg-opacity-75" @click="sidebarOpen = false"></div>
    </div>

    <div class="flex h-full">
        <!-- Sidebar -->
        <nav class="w-64 shadow-lg transform lg:translate-x-0 transition-transform duration-300 ease-in-out fixed inset-y-0 left-0 z-50 lg:static lg:inset-0"
             :class="{'translate-x-0': sidebarOpen, '-translate-x-full': !sidebarOpen}"
             style="background-color: var(--elevated-dark) !important; border-right: 1px solid var(--surface-dark);"
             role="navigation"
             aria-label="Main navigation">
            <div class="flex items-center justify-between p-4" style="border-bottom: 1px solid var(--surface-dark);">
                <div class="flex items-center">
                    <div class="w-8 h-8 rounded-lg flex items-center justify-center mr-3" style="background-color: var(--firebase-orange);">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <h1 class="text-xl font-bold" style="color: var(--text-primary) !important;">RAG Dashboard</h1>
                </div>
                <button @click="sidebarOpen = false" 
                        class="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                        aria-label="Close sidebar">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <!-- Navigation Links -->
            <nav class="mt-6 px-3" role="navigation" aria-label="Primary navigation">
                <ul class="space-y-1" role="list">
                    {% set nav_items = [
                        {'url': '/dashboard', 'name': 'Dashboard', 'icon': 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z', 'description': 'Main dashboard overview'},
                        {'url': '/crawl', 'name': 'Web Crawling', 'icon': 'M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9', 'description': 'Crawl websites and add content'},
                        {'url': '/query', 'name': 'RAG Query', 'icon': 'M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z', 'description': 'Search and query content'},
                        {'url': '/content', 'name': 'Content', 'icon': 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z', 'description': 'Manage crawled content and sources'},
                        {'url': '/stats', 'name': 'Statistics', 'icon': 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z', 'description': 'View system statistics and analytics'},
                        {'url': '/settings', 'name': 'Settings', 'icon': 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z', 'description': 'Configure system settings'},
                        {'url': '/health', 'name': 'Health', 'icon': 'M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z', 'description': 'System health and status monitoring'}
                    ] %}
                    
                    {% for item in nav_items %}
                    <li>
                        {% set is_active = request.url.path == item.url or (item.url != '/dashboard' and request.url.path.startswith(item.url)) %}
                        <a href="{{ item.url }}" 
                           class="firebase-nav-link group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2
                                  {% if is_active %}active{% endif %}"
                           {% if is_active %}aria-current="page"{% endif %}
                           aria-describedby="nav-desc-{{ loop.index }}"
                           :style="`${isActive ? 'background-color: var(--surface-dark) !important; color: var(--firebase-orange) !important; border-left: 3px solid var(--firebase-orange);' : 'color: var(--text-secondary) !important;'}`">
                            <svg class="mr-3 flex-shrink-0 h-5 w-5 transition-colors duration-200" 
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true"
                                 :style="`color: ${isActive ? 'var(--firebase-orange)' : 'var(--text-muted)'} !important;`">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ item.icon }}"></path>
                            </svg>
                            <span>{{ item.name }}</span>
                            <span id="nav-desc-{{ loop.index }}" class="sr-only">{{ item.description }}</span>
                        </a>
                    </li>
                    {% endfor %}
                </ul>
            </nav>
            
            <!-- Status indicator -->
            <div class="absolute bottom-0 left-0 right-0 p-4" style="border-top: 1px solid var(--surface-dark); background-color: var(--elevated-dark);">
                <!-- Status indicator -->
                <div class="text-xs firebase-status firebase-status-success"
                     role="status"
                     aria-live="polite"
                     aria-label="System status">
                    <div class="flex items-center">
                        <div class="w-2 h-2 rounded-full mr-2" style="background-color: var(--android-green);"></div>
                        <span style="color: var(--text-primary) !important;">Server Online</span>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Main content -->
        <main class="flex-1 flex flex-col min-w-0 overflow-hidden lg:ml-0" id="main-content">
            <!-- Mobile header -->
            <header class="bg-white shadow-sm border-b lg:hidden" role="banner">
                <div class="flex items-center justify-between px-4 py-3">
                    <button @click="sidebarOpen = true" 
                            class="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                            aria-label="Open sidebar"
                            aria-expanded="false"
                            aria-controls="sidebar">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                    <h1 class="text-lg font-semibold text-gray-800 truncate">{{ title | default('Dashboard') }}</h1>
                    <div class="w-10"></div> <!-- Spacer for centering -->
                </div>
            </header>
            
            <!-- Desktop header -->
            <header class="shadow-sm border-b hidden lg:block" role="banner" style="background-color: var(--elevated-dark) !important; border-color: var(--surface-dark) !important;">
                <div class="px-4 sm:px-6 lg:px-8 py-4">
                    <div class="flex items-center justify-between">
                        <div class="min-w-0 flex-1">
                            <h1 class="text-2xl font-semibold truncate" style="color: var(--text-primary) !important;">
                                {% block page_title %}{{ title | default('Dashboard') }}{% endblock %}
                            </h1>
                            {% if subtitle %}
                            <p class="mt-1 text-sm truncate" style="color: var(--text-secondary) !important;">{{ subtitle }}</p>
                            {% endif %}
                            {% if current_time %}
                            <p class="text-sm mt-1" aria-label="Last updated time" style="color: var(--text-secondary) !important;">
                                Last updated: <time datetime="{{ current_time.isoformat() }}">{{ current_time.strftime('%H:%M:%S') }}</time>
                            </p>
                            {% endif %}
                        </div>
                        
                        <!-- Quick actions and status -->
                        <div class="flex items-center space-x-4">
                            {% block header_actions %}{% endblock %}
                            
                            <!-- Real-time updates indicator -->
                            <div class="flex items-center space-x-2">
                                <div class="flex items-center" role="status" aria-label="Real-time updates status">
                                    <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse" aria-hidden="true"></div>
                                    <span class="text-xs text-gray-500 font-medium">Live</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Content area -->
            <div class="flex-1 overflow-auto" style="background-color: var(--deep-dark) !important;">
                <div class="p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto">
                    <!-- Alert messages -->
                    {% if system_alerts %}
                    <div class="mb-6 space-y-3" role="alert" aria-live="polite">
                        {% for alert in system_alerts %}
                        <div class="rounded-lg p-4 {% if alert.type == 'error' %}bg-red-50 border border-red-200{% elif alert.type == 'warning' %}bg-yellow-50 border border-yellow-200{% elif alert.type == 'success' %}bg-green-50 border border-green-200{% else %}bg-blue-50 border border-blue-200{% endif %}">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    {% if alert.type == 'error' %}
                                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                    {% elif alert.type == 'warning' %}
                                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                    {% elif alert.type == 'success' %}
                                    <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                    {% else %}
                                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                    </svg>
                                    {% endif %}
                                </div>
                                <div class="ml-3 flex-1">
                                    <p class="text-sm font-medium {% if alert.type == 'error' %}text-red-800{% elif alert.type == 'warning' %}text-yellow-800{% elif alert.type == 'success' %}text-green-800{% else %}text-blue-800{% endif %}">
                                        {{ alert.message }}
                                    </p>
                                    {% if alert.details %}
                                    <p class="mt-1 text-sm {% if alert.type == 'error' %}text-red-700{% elif alert.type == 'warning' %}text-yellow-700{% elif alert.type == 'success' %}text-green-700{% else %}text-blue-700{% endif %}">
                                        {{ alert.details }}
                                    </p>
                                    {% endif %}
                                </div>
                                {% if alert.dismissible %}
                                <div class="ml-auto pl-3">
                                    <button class="inline-flex rounded-md p-1.5 {% if alert.type == 'error' %}text-red-500 hover:bg-red-100{% elif alert.type == 'warning' %}text-yellow-500 hover:bg-yellow-100{% elif alert.type == 'success' %}text-green-500 hover:bg-green-100{% else %}text-blue-500 hover:bg-blue-100{% endif %} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                                            hx-delete="/htmx/alerts/{{ alert.id }}"
                                            hx-target="closest div"
                                            hx-swap="outerHTML"
                                            aria-label="Dismiss alert">
                                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z" />
                                        </svg>
                                    </button>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <!-- Main content -->
                    <div class="space-y-6">
                        {% block content %}{% endblock %}
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Footer -->
    <footer class="border-t mt-auto" role="contentinfo" :style="`background-color: var(--bg-secondary); border-color: var(--border-color);`">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex flex-col sm:flex-row justify-between items-center text-xs" :style="`color: var(--text-secondary);`">
                <p>&copy; {{ current_year() }} HTMX RAG Dashboard. Version 1.0.0</p>
                <div class="flex items-center space-x-4 mt-2 sm:mt-0">
                    <span>Status: <span class="font-medium" :style="`color: var(--android-green);`">Online</span></span>
                    <span>•</span>
                    <a href="/health" class="focus:outline-none focus:underline transition-colors" :style="`color: var(--text-secondary);`" 
                       @mouseover="$event.target.style.color = 'var(--firebase-orange)'"
                       @mouseout="$event.target.style.color = 'var(--text-secondary)'">System Health</a>
                </div>
            </div>
        </div>
    </footer>
    
    {% block scripts %}
    <script>
        // Global HTMX configuration
        document.body.addEventListener('htmx:configRequest', function(evt) {
            evt.detail.headers['X-Requested-With'] = 'XMLHttpRequest';
            evt.detail.headers['X-CSRFToken'] = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
        });
        
        // Handle HTMX errors with better UX
        document.body.addEventListener('htmx:responseError', function(evt) {
            console.error('HTMX Error:', evt.detail);
            showNotification('An error occurred. Please try again.', 'error');
        });
        
        // Handle HTMX network errors
        document.body.addEventListener('htmx:sendError', function(evt) {
            console.error('HTMX Network Error:', evt.detail);
            showNotification('Network error. Please check your connection.', 'error');
        });
        
        // Show loading states
        document.body.addEventListener('htmx:beforeRequest', function(evt) {
            const target = evt.target;
            if (target.hasAttribute('data-loading-text')) {
                target.setAttribute('data-original-text', target.textContent);
                target.textContent = target.getAttribute('data-loading-text');
                target.disabled = true;
            }
        });
        
        document.body.addEventListener('htmx:afterRequest', function(evt) {
            const target = evt.target;
            if (target.hasAttribute('data-original-text')) {
                target.textContent = target.getAttribute('data-original-text');
                target.removeAttribute('data-original-text');
                target.disabled = false;
            }
        });
        
        // Notification system
        function showNotification(message, type = 'info', duration = 5000) {
            const notification = document.createElement('div');
            const bgColor = {
                'success': 'bg-green-100 border-green-400 text-green-700',
                'error': 'bg-red-100 border-red-400 text-red-700',
                'warning': 'bg-yellow-100 border-yellow-400 text-yellow-700',
                'info': 'bg-blue-100 border-blue-400 text-blue-700'
            }[type] || 'bg-blue-100 border-blue-400 text-blue-700';
            
            notification.className = `fixed top-4 right-4 ${bgColor} border px-4 py-3 rounded-lg shadow-lg z-50 max-w-sm transform transition-all duration-300 translate-x-full`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <span class="flex-1">${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-current opacity-70 hover:opacity-100 focus:outline-none">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // Animate in
            setTimeout(() => notification.classList.remove('translate-x-full'), 100);
            
            // Auto remove
            if (duration > 0) {
                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => notification.remove(), 300);
                }, duration);
            }
        }
        
        // Keyboard navigation improvements
        document.addEventListener('keydown', function(e) {
            // ESC to close sidebar on mobile
            if (e.key === 'Escape' && window.innerWidth < 1024) {
                const sidebarOpen = document.querySelector('[x-data]').__x.$data.sidebarOpen;
                if (sidebarOpen) {
                    document.querySelector('[x-data]').__x.$data.sidebarOpen = false;
                }
            }
        });
        
        // Auto-refresh functionality with better error handling
        let refreshInterval;
        function startAutoRefresh() {
            if (refreshInterval) clearInterval(refreshInterval);
            refreshInterval = setInterval(function() {
                document.querySelectorAll('[data-auto-refresh]').forEach(function(el) {
                    if (document.visibilityState === 'visible') {
                        htmx.trigger(el, 'refresh');
                    }
                });
            }, 10000); // 10 seconds default
        }
        
        // Start auto-refresh when page is visible
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                startAutoRefresh();
            } else {
                if (refreshInterval) clearInterval(refreshInterval);
            }
        });
        
        // Initialize auto-refresh
        if (document.visibilityState === 'visible') {
            startAutoRefresh();
        }
        
        // Accessibility improvements
        document.addEventListener('DOMContentLoaded', function() {
            // Add focus visible polyfill behavior
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Tab') {
                    document.body.classList.add('keyboard-navigation');
                }
            });
            
            document.addEventListener('mousedown', function() {
                document.body.classList.remove('keyboard-navigation');
            });
        });
    </script>
    {% endblock %}
</body>
</html>