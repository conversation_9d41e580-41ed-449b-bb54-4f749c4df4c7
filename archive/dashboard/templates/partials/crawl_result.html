<!-- Crawl Result Partial -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Crawl Complete</h3>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                     {% if result.success %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
            {% if result.success %}
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                Success
            {% else %}
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
                Failed
            {% endif %}
        </span>
    </div>
    
    {% if result.success %}
    <!-- Success Results -->
    <div class="space-y-4">
        <!-- URL -->
        <div>
            <p class="text-sm font-medium text-gray-700">URL:</p>
            <p class="text-sm text-gray-900 break-all">{{ result.url }}</p>
        </div>
        
        <!-- Summary Statistics -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="bg-blue-50 p-3 rounded-md">
                <p class="text-2xl font-semibold text-blue-900">{{ result.summary.chunks_stored }}</p>
                <p class="text-sm text-blue-700">Chunks Stored</p>
            </div>
            
            <div class="bg-green-50 p-3 rounded-md">
                <p class="text-2xl font-semibold text-green-900">{{ result.summary.code_examples_stored }}</p>
                <p class="text-sm text-green-700">Code Examples</p>
            </div>
            
            <div class="bg-purple-50 p-3 rounded-md">
                <p class="text-2xl font-semibold text-purple-900">{{ result.summary.total_word_count | default(0) }}</p>
                <p class="text-sm text-purple-700">Total Words</p>
            </div>
            
            <div class="bg-orange-50 p-3 rounded-md">
                <p class="text-2xl font-semibold text-orange-900">{{ "%.1f"|format(duration) }}s</p>
                <p class="text-sm text-orange-700">Duration</p>
            </div>
        </div>
        
        <!-- Links Found -->
        {% if result.links_count %}
        <div>
            <p class="text-sm font-medium text-gray-700 mb-2">Links Found:</p>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                {% for link_type, count in result.links_count.items() %}
                <div class="flex justify-between items-center bg-gray-50 px-3 py-2 rounded-md">
                    <span class="text-sm text-gray-600">{{ link_type.title() }}</span>
                    <span class="text-sm font-medium text-gray-900">{{ count }}</span>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- Source ID -->
        {% if result.source_id %}
        <div>
            <p class="text-sm font-medium text-gray-700">Source ID:</p>
            <p class="text-sm text-gray-900 font-mono">{{ result.source_id }}</p>
        </div>
        {% endif %}
    </div>
    
    {% else %}
    <!-- Error Results -->
    <div class="space-y-4">
        <!-- URL -->
        <div>
            <p class="text-sm font-medium text-gray-700">URL:</p>
            <p class="text-sm text-gray-900 break-all">{{ result.url }}</p>
        </div>
        
        <!-- Error Message -->
        <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Crawl Failed</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <p>{{ result.error or "Unknown error occurred during crawling" }}</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Duration -->
        <div class="text-sm text-gray-600">
            <span>Duration: {{ "%.1f"|format(duration) }} seconds</span>
        </div>
    </div>
    {% endif %}
    
    <!-- Actions -->
    <div class="mt-6 flex justify-end space-x-3">
        {% if result.success %}
        <button type="button" 
                onclick="document.getElementById('crawl-form').reset(); document.getElementById('crawl-results').innerHTML = '';"
                class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            Crawl Another URL
        </button>
        {% else %}
        <button type="button" 
                onclick="document.getElementById('crawl-results').innerHTML = '';"
                class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            Try Again
        </button>
        {% endif %}
    </div>
</div>