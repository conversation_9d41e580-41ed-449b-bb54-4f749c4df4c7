{% if server_available and rag_strategy_usage %}
<div class="space-y-6">
    {% for strategy_name, strategy_data in rag_strategy_usage.items() %}
    <div class="border border-gray-200 rounded-lg p-4 {% if strategy_data.enabled %}bg-green-50 border-green-200{% else %}bg-gray-50{% endif %}">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
                <div class="flex-shrink-0 mr-3">
                    {% if strategy_data.enabled %}
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    {% else %}
                    <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                    {% endif %}
                </div>
                <div>
                    <h5 class="font-medium text-gray-900">
                        {% if strategy_name == 'contextual_embeddings' %}
                        Contextual Embeddings
                        {% elif strategy_name == 'hybrid_search' %}
                        Hybrid Search
                        {% elif strategy_name == 'agentic_rag' %}
                        Agentic RAG
                        {% elif strategy_name == 'reranking' %}
                        Reranking
                        {% else %}
                        {{ strategy_name|title|replace('_', ' ') }}
                        {% endif %}
                    </h5>
                    <p class="text-sm {% if strategy_data.enabled %}text-green-700{% else %}text-gray-600{% endif %}">
                        {% if strategy_data.enabled %}Enabled{% else %}Disabled{% endif %}
                    </p>
                </div>
            </div>
            
            {% if strategy_data.enabled %}
            <div class="text-right">
                <p class="text-lg font-bold text-gray-900">{{ strategy_data.usage_count|default(0) }}</p>
                <p class="text-sm text-gray-600">uses</p>
            </div>
            {% endif %}
        </div>
        
        {% if strategy_data.enabled %}
        <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
                <span class="text-gray-600">Success Rate:</span>
                <span class="font-medium {% if strategy_data.success_rate >= 0.9 %}text-green-600{% elif strategy_data.success_rate >= 0.7 %}text-yellow-600{% else %}text-red-600{% endif %}">
                    {{ (strategy_data.success_rate * 100)|round(1) if strategy_data.success_rate else 0 }}%
                </span>
            </div>
            <div>
                <span class="text-gray-600">Total Uses:</span>
                <span class="font-medium text-gray-900">{{ strategy_data.usage_count|default(0) }}</span>
            </div>
        </div>
        
        <!-- Success rate progress bar -->
        <div class="mt-3">
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="h-2 rounded-full transition-all duration-300 {% if strategy_data.success_rate >= 0.9 %}bg-green-500{% elif strategy_data.success_rate >= 0.7 %}bg-yellow-500{% else %}bg-red-500{% endif %}" 
                     style="width: {{ (strategy_data.success_rate * 100) if strategy_data.success_rate else 0 }}%"></div>
            </div>
        </div>
        {% else %}
        <div class="text-sm text-gray-600">
            <p>This strategy is currently disabled. Enable it in the settings to start collecting usage statistics.</p>
        </div>
        {% endif %}
        
        <!-- Strategy description -->
        <div class="mt-3 text-xs text-gray-500">
            {% if strategy_name == 'contextual_embeddings' %}
            Uses context-aware embeddings for better semantic understanding
            {% elif strategy_name == 'hybrid_search' %}
            Combines keyword and semantic search for improved results
            {% elif strategy_name == 'agentic_rag' %}
            Employs AI agents for complex query processing and reasoning
            {% elif strategy_name == 'reranking' %}
            Reorders search results based on relevance scoring
            {% endif %}
        </div>
    </div>
    {% endfor %}
</div>

<!-- Strategy Performance Summary -->
{% set enabled_strategies = rag_strategy_usage.values()|selectattr('enabled')|list %}
{% if enabled_strategies %}
<div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
    <h5 class="font-medium text-blue-900 mb-2">Strategy Performance Summary</h5>
    <div class="grid grid-cols-2 gap-4 text-sm">
        <div>
            <span class="text-blue-700">Active Strategies:</span>
            <span class="font-medium text-blue-900">{{ enabled_strategies|length }}/{{ rag_strategy_usage|length }}</span>
        </div>
        <div>
            <span class="text-blue-700">Total Usage:</span>
            <span class="font-medium text-blue-900">{{ enabled_strategies|sum(attribute='usage_count')|default(0) }}</span>
        </div>
    </div>
    
    {% set avg_success_rate = (enabled_strategies|sum(attribute='success_rate')|default(0) / enabled_strategies|length) if enabled_strategies else 0 %}
    <div class="mt-2">
        <div class="flex items-center justify-between text-sm">
            <span class="text-blue-700">Overall Success Rate:</span>
            <span class="font-medium text-blue-900">{{ (avg_success_rate * 100)|round(1) }}%</span>
        </div>
        <div class="w-full bg-blue-200 rounded-full h-2 mt-1">
            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: {{ (avg_success_rate * 100) }}%"></div>
        </div>
    </div>
</div>
{% endif %}

{% else %}
<!-- No RAG strategy data available -->
<div class="text-center py-8">
    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
    </svg>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No RAG Strategy Data</h3>
    <p class="text-gray-600">
        {% if not server_available %}
        Server is not available. Please check the connection.
        {% else %}
        RAG strategy usage statistics will appear after queries are processed.
        {% endif %}
    </p>
    {% if server_available %}
    <div class="mt-4">
        <a href="/settings" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            Configure Strategies
        </a>
    </div>
    {% endif %}
</div>
{% endif %}