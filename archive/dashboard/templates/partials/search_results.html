<!-- Search Results Partial Template -->
{% if success %}
    {% if has_results %}
        <!-- Search Summary -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-sm font-medium text-blue-800">Search Results</h4>
                    <p class="text-sm text-blue-700">
                        Found {{ total_results }} result{{ 's' if total_results != 1 else '' }} for "{{ query }}" 
                        in {{ execution_time }}s
                    </p>
                </div>
                <button class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                        hx-post="/htmx/query/save"
                        hx-vals='{"query": "{{ query }}", "results_count": {{ total_results }}}'
                        hx-target="#save-query-status"
                        hx-swap="innerHTML">
                    Save Query
                </button>
            </div>
            <div id="save-query-status" class="mt-2"></div>
        </div>
        
        <!-- Search Results -->
        <div class="space-y-4">
            {% for result in results %}
            <div class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <div class="p-4">
                    <!-- Result Header -->
                    <div class="flex items-start justify-between mb-2">
                        <div class="flex-1">
                            <h5 class="text-lg font-medium text-gray-900 mb-1">
                                {% if result.title %}
                                    {{ result.title }}
                                {% else %}
                                    Document Result
                                {% endif %}
                            </h5>
                            <div class="flex items-center space-x-4 text-sm text-gray-600">
                                {% if result.source_url %}
                                <a href="{{ result.source_url }}" target="_blank" class="text-blue-600 hover:text-blue-800 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                    </svg>
                                    {{ result.source_url | truncate(50) }}
                                </a>
                                {% endif %}
                                
                                {% if result.chunk_id %}
                                <span class="text-gray-500">Chunk: {{ result.chunk_id }}</span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Relevance Scores -->
                        <div class="flex flex-col items-end space-y-1">
                            {% if result.similarity_score is defined %}
                            <div class="flex items-center">
                                <span class="text-xs text-gray-500 mr-2">Similarity:</span>
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    {% if result.similarity_score >= 0.8 %}bg-green-100 text-green-800
                                    {% elif result.similarity_score >= 0.6 %}bg-yellow-100 text-yellow-800
                                    {% else %}bg-red-100 text-red-800{% endif %}">
                                    {{ "%.3f"|format(result.similarity_score) }}
                                </span>
                            </div>
                            {% endif %}
                            
                            {% if result.rerank_score is defined %}
                            <div class="flex items-center">
                                <span class="text-xs text-gray-500 mr-2">Rerank:</span>
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    {% if result.rerank_score >= 0.8 %}bg-green-100 text-green-800
                                    {% elif result.rerank_score >= 0.6 %}bg-yellow-100 text-yellow-800
                                    {% else %}bg-red-100 text-red-800{% endif %}">
                                    {{ "%.3f"|format(result.rerank_score) }}
                                </span>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Content Preview -->
                    <div class="mb-3">
                        <p class="text-gray-700 text-sm leading-relaxed">
                            {{ result.content | truncate(300) }}
                        </p>
                    </div>
                    
                    <!-- Metadata -->
                    {% if result.metadata %}
                    <div class="flex flex-wrap gap-2 mb-3">
                        {% for key, value in result.metadata.items() %}
                        {% if key not in ['content', 'chunk_id', 'source_url'] %}
                        <span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
                            {{ key }}: {{ value }}
                        </span>
                        {% endif %}
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <!-- Actions -->
                    <div class="flex items-center justify-between pt-3 border-t border-gray-100">
                        <button class="text-blue-600 hover:text-blue-800 text-sm font-medium expand-result"
                                data-result-id="{{ loop.index0 }}">
                            <span class="expand-text">Show Full Content</span>
                            <span class="collapse-text hidden">Hide Full Content</span>
                        </button>
                        
                        <div class="flex items-center space-x-3">
                            {% if result.source_url %}
                            <a href="{{ result.source_url }}" target="_blank" 
                               class="text-gray-600 hover:text-gray-800 text-sm">
                                View Source
                            </a>
                            {% endif %}
                            
                            <button class="text-gray-600 hover:text-gray-800 text-sm"
                                    onclick="copyToClipboard('{{ result.content | replace("'", "\\'") | replace('"', '\\"') }}')">
                                Copy
                            </button>
                        </div>
                    </div>
                    
                    <!-- Full Content (Initially Hidden) -->
                    <div class="full-content hidden mt-4 pt-4 border-t border-gray-100">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <pre class="text-sm text-gray-800 whitespace-pre-wrap">{{ result.content }}</pre>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- Load More Results (if applicable) -->
        {% if total_results > results|length %}
        <div class="text-center mt-6">
            <button class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md font-medium"
                    hx-post="/htmx/query/load-more"
                    hx-vals='{"query": "{{ query }}", "offset": {{ results|length }}}'
                    hx-target="#search-results"
                    hx-swap="beforeend">
                Load More Results ({{ total_results - results|length }} remaining)
            </button>
        </div>
        {% endif %}
        
    {% else %}
        <!-- No Results -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
            <div class="flex flex-col items-center">
                <svg class="w-12 h-12 text-yellow-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                <h4 class="text-lg font-medium text-yellow-800 mb-2">No Results Found</h4>
                <p class="text-yellow-700 mb-4">
                    No documents found matching "{{ query }}". Try adjusting your search terms or filters.
                </p>
                <div class="text-sm text-yellow-600">
                    <p>Search completed in {{ execution_time }}s</p>
                </div>
            </div>
        </div>
    {% endif %}
    
{% else %}
    <!-- Error State -->
    <div class="bg-red-50 border border-red-200 rounded-lg p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Search Failed</h3>
                <p class="text-sm text-red-700 mt-1">
                    {% if error %}
                        {{ error }}
                    {% else %}
                        An error occurred while searching. Please try again.
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
{% endif %}

<script>
// Expand/collapse result content
document.querySelectorAll('.expand-result').forEach(button => {
    button.addEventListener('click', function() {
        const resultId = this.getAttribute('data-result-id');
        const fullContent = this.closest('.bg-white').querySelector('.full-content');
        const expandText = this.querySelector('.expand-text');
        const collapseText = this.querySelector('.collapse-text');
        
        if (fullContent.classList.contains('hidden')) {
            fullContent.classList.remove('hidden');
            expandText.classList.add('hidden');
            collapseText.classList.remove('hidden');
        } else {
            fullContent.classList.add('hidden');
            expandText.classList.remove('hidden');
            collapseText.classList.add('hidden');
        }
    });
});

// Copy to clipboard function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show temporary success message
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg z-50';
        notification.textContent = 'Content copied to clipboard!';
        document.body.appendChild(notification);
        
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 2000);
    }).catch(function(err) {
        console.error('Failed to copy text: ', err);
    });
}
</script>