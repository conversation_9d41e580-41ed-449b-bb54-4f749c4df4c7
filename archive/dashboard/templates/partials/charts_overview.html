<!-- Charts Overview Section with Historical Data Visualization -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Query Response Time Chart -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between mb-4">
            <h4 class="text-lg font-semibold text-gray-900">Query Response Times</h4>
            <div class="flex items-center space-x-2">
                <select class="text-sm border-gray-300 rounded-md" id="response-time-period">
                    <option value="1h">Last Hour</option>
                    <option value="24h" selected>Last 24 Hours</option>
                    <option value="7d">Last 7 Days</option>
                </select>
            </div>
        </div>
        <div class="h-64" id="response-time-chart">
            <!-- Chart will be rendered here -->
            <canvas id="responseTimeCanvas" width="400" height="200"></canvas>
        </div>
        <div class="mt-4 grid grid-cols-3 gap-4 text-center">
            <div>
                <p class="text-sm text-gray-600">Average</p>
                <p class="text-lg font-semibold text-blue-600">{{ (performance_metrics.query_response_times.avg * 1000)|round|int }}ms</p>
            </div>
            <div>
                <p class="text-sm text-gray-600">95th Percentile</p>
                <p class="text-lg font-semibold text-orange-600">{{ (performance_metrics.query_response_times.p95 * 1000)|round|int }}ms</p>
            </div>
            <div>
                <p class="text-sm text-gray-600">Max</p>
                <p class="text-lg font-semibold text-red-600">{{ (performance_metrics.query_response_times.max * 1000)|round|int }}ms</p>
            </div>
        </div>
    </div>

    <!-- Crawling Activity Chart -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between mb-4">
            <h4 class="text-lg font-semibold text-gray-900">Crawling Activity</h4>
            <div class="flex items-center space-x-2">
                <select class="text-sm border-gray-300 rounded-md" id="crawl-activity-period">
                    <option value="24h">Last 24 Hours</option>
                    <option value="7d" selected>Last 7 Days</option>
                    <option value="30d">Last 30 Days</option>
                </select>
            </div>
        </div>
        <div class="h-64" id="crawl-activity-chart">
            <canvas id="crawlActivityCanvas" width="400" height="200"></canvas>
        </div>
        <div class="mt-4 grid grid-cols-2 gap-4 text-center">
            <div>
                <p class="text-sm text-gray-600">Success Rate</p>
                <p class="text-lg font-semibold text-green-600">{{ (performance_metrics.crawl_performance.success_rate * 100)|round(1) }}%</p>
            </div>
            <div>
                <p class="text-sm text-gray-600">Avg Pages/Min</p>
                <p class="text-lg font-semibold text-blue-600">{{ performance_metrics.crawl_performance.avg_pages_per_minute|round(1) }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Content Growth Chart -->
<div class="bg-white rounded-lg shadow p-6 mb-8">
    <div class="flex items-center justify-between mb-4">
        <h4 class="text-lg font-semibold text-gray-900">Content Growth Over Time</h4>
        <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span class="text-sm text-gray-600">Documents</span>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                <span class="text-sm text-gray-600">Code Examples</span>
            </div>
            <select class="text-sm border-gray-300 rounded-md" id="content-growth-period">
                <option value="7d">Last 7 Days</option>
                <option value="30d" selected>Last 30 Days</option>
                <option value="90d">Last 90 Days</option>
            </select>
        </div>
    </div>
    <div class="h-80" id="content-growth-chart">
        <canvas id="contentGrowthCanvas" width="800" height="300"></canvas>
    </div>
</div>

<!-- System Resources Chart -->
<div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center justify-between mb-4">
        <h4 class="text-lg font-semibold text-gray-900">System Resources</h4>
        <div class="text-sm text-gray-500">Real-time monitoring</div>
    </div>
    <div class="h-64" id="system-resources-chart">
        <canvas id="systemResourcesCanvas" width="400" height="200"></canvas>
    </div>
    <div class="mt-4 grid grid-cols-3 gap-4">
        <div class="text-center p-3 bg-blue-50 rounded-lg">
            <p class="text-sm text-blue-700 font-medium">CPU Usage</p>
            <p class="text-xl font-bold text-blue-900">{{ performance_metrics.system_resources.cpu_usage|round(1) }}%</p>
            <div class="w-full bg-blue-200 rounded-full h-2 mt-2">
                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: {{ performance_metrics.system_resources.cpu_usage }}%"></div>
            </div>
        </div>
        <div class="text-center p-3 bg-green-50 rounded-lg">
            <p class="text-sm text-green-700 font-medium">Memory Usage</p>
            <p class="text-xl font-bold text-green-900">{{ performance_metrics.system_resources.memory_usage|round(1) }}%</p>
            <div class="w-full bg-green-200 rounded-full h-2 mt-2">
                <div class="bg-green-600 h-2 rounded-full transition-all duration-300" style="width: {{ performance_metrics.system_resources.memory_usage }}%"></div>
            </div>
        </div>
        <div class="text-center p-3 bg-purple-50 rounded-lg">
            <p class="text-sm text-purple-700 font-medium">Disk Usage</p>
            <p class="text-xl font-bold text-purple-900">{{ performance_metrics.system_resources.disk_usage|round(1) }}%</p>
            <div class="w-full bg-purple-200 rounded-full h-2 mt-2">
                <div class="bg-purple-600 h-2 rounded-full transition-all duration-300" style="width: {{ performance_metrics.system_resources.disk_usage }}%"></div>
            </div>
        </div>
    </div>
</div>

<script>
// Chart.js initialization and data management
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all charts
    initializeResponseTimeChart();
    initializeCrawlActivityChart();
    initializeContentGrowthChart();
    initializeSystemResourcesChart();
    
    // Set up real-time updates for system resources
    setInterval(updateSystemResourcesChart, 5000);
});

function initializeResponseTimeChart() {
    const ctx = document.getElementById('responseTimeCanvas').getContext('2d');
    
    // Check if server is available and has performance data
    const performanceMetrics = {{ performance_metrics|tojson if performance_metrics else 'null' }};
    
    if (!performanceMetrics || !performanceMetrics.query_response_times || !performanceMetrics.query_response_times.recent_data) {
        // Show error message instead of chart
        ctx.canvas.parentElement.innerHTML = '<div class="flex items-center justify-center h-full text-gray-500"><p>No response time data available. Server may be disconnected.</p></div>';
        return;
    }
    
    const recentData = performanceMetrics.query_response_times.recent_data;
    const labels = recentData.map(item => new Date(item.timestamp).toLocaleTimeString());
    const data = recentData.map(item => item.response_time);
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Response Time (ms)',
                data: data,
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Response Time (ms)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Time'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

function initializeCrawlActivityChart() {
    const ctx = document.getElementById('crawlActivityCanvas').getContext('2d');
    
    // Check if server is available and has crawl performance data
    const performanceMetrics = {{ performance_metrics|tojson if performance_metrics else 'null' }};
    
    if (!performanceMetrics || !performanceMetrics.crawl_performance || !performanceMetrics.crawl_performance.recent_crawls) {
        // Show error message instead of chart
        ctx.canvas.parentElement.innerHTML = '<div class="flex items-center justify-center h-full text-gray-500"><p>No crawl activity data available. Server may be disconnected.</p></div>';
        return;
    }
    
    const recentCrawls = performanceMetrics.crawl_performance.recent_crawls;
    const labels = recentCrawls.map(item => new Date(item.date).toLocaleDateString('en-US', { weekday: 'short' }));
    const successData = recentCrawls.map(item => item.successful || 0);
    const failureData = recentCrawls.map(item => item.failed || 0);
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Successful Crawls',
                data: successData,
                backgroundColor: 'rgba(34, 197, 94, 0.8)',
                borderColor: 'rgb(34, 197, 94)',
                borderWidth: 1
            }, {
                label: 'Failed Crawls',
                data: failureData,
                backgroundColor: 'rgba(239, 68, 68, 0.8)',
                borderColor: 'rgb(239, 68, 68)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Crawls'
                    }
                }
            }
        }
    });
}

function initializeContentGrowthChart() {
    const ctx = document.getElementById('contentGrowthCanvas').getContext('2d');
    
    // Check if server is available and has content breakdown data
    const contentBreakdown = {{ content_breakdown|tojson if content_breakdown else 'null' }};
    
    if (!contentBreakdown || !contentBreakdown.by_date) {
        // Show error message instead of chart
        ctx.canvas.parentElement.innerHTML = '<div class="flex items-center justify-center h-full text-gray-500"><p>No content growth data available. Server may be disconnected.</p></div>';
        return;
    }
    
    const byDateData = contentBreakdown.by_date;
    const labels = byDateData.map(item => new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
    const documentsData = byDateData.map(item => item.total_documents || 0);
    const codeExamplesData = byDateData.map(item => item.code_examples || 0);
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Total Documents',
                data: documentsData,
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: false
            }, {
                label: 'Code Examples',
                data: codeExamplesData,
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.4,
                fill: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Count'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Date'
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

function initializeSystemResourcesChart() {
    const ctx = document.getElementById('systemResourcesCanvas').getContext('2d');
    
    // Check if server is available and has system resource data
    const performanceMetrics = {{ performance_metrics|tojson if performance_metrics else 'null' }};
    
    if (!performanceMetrics || !performanceMetrics.system_resources) {
        // Show error message instead of chart
        ctx.canvas.parentElement.innerHTML = '<div class="flex items-center justify-center h-full text-gray-500"><p>No system resource data available. Server may be disconnected.</p></div>';
        return;
    }
    
    const systemResources = performanceMetrics.system_resources;
    const currentTime = new Date().toLocaleTimeString('en-US', { hour12: false, minute: '2-digit', second: '2-digit' });
    
    window.systemResourcesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [currentTime],
            datasets: [{
                label: 'CPU %',
                data: [systemResources.cpu_usage || 0],
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4
            }, {
                label: 'Memory %',
                data: [systemResources.memory_usage || 0],
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.4
            }, {
                label: 'Disk %',
                data: [systemResources.disk_usage || 0],
                borderColor: 'rgb(168, 85, 247)',
                backgroundColor: 'rgba(168, 85, 247, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'Usage %'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Time'
                    }
                }
            },
            animation: {
                duration: 0
            }
        }
    });
}

function updateSystemResourcesChart() {
    if (window.systemResourcesChart) {
        // For real-time updates, this would need to fetch fresh data from the server
        // For now, we'll skip automatic updates since we're using real data
        // In a production environment, this could make an AJAX call to get current system metrics
        console.log('System resources chart update skipped - using real data only');
    }
}
</script>