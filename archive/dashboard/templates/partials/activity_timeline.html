{% if server_available and activity_timeline %}
<div class="space-y-4 max-h-96 overflow-y-auto">
    {% for activity in activity_timeline[:20] %}
    <div class="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
        <!-- Activity Icon -->
        <div class="flex-shrink-0 mt-1">
            {% if activity.type == 'crawl' %}
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                </svg>
            </div>
            {% elif activity.type == 'query' %}
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>
            {% elif activity.type == 'error' %}
            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            {% elif activity.type == 'system' %}
            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                </svg>
            </div>
            {% else %}
            <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            {% endif %}
        </div>
        
        <!-- Activity Content -->
        <div class="flex-1 min-w-0">
            <div class="flex items-center justify-between">
                <p class="text-sm font-medium text-gray-900 truncate">
                    {{ activity.title|default('Unknown Activity') }}
                </p>
                <div class="flex items-center space-x-2">
                    <!-- Status Badge -->
                    {% if activity.status %}
                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium {% if activity.status == 'success' %}bg-green-100 text-green-800{% elif activity.status == 'error' %}bg-red-100 text-red-800{% elif activity.status == 'warning' %}bg-yellow-100 text-yellow-800{% elif activity.status == 'in_progress' %}bg-blue-100 text-blue-800{% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ activity.status|title|replace('_', ' ') }}
                    </span>
                    {% endif %}
                    
                    <!-- Timestamp -->
                    <span class="text-xs text-gray-500">
                        {{ activity.timestamp if activity.timestamp else 'Unknown time' }}
                    </span>
                </div>
            </div>
            
            {% if activity.description %}
            <p class="text-sm text-gray-600 mt-1">{{ activity.description }}</p>
            {% endif %}
            
            <!-- Activity Details -->
            {% if activity.details %}
            <div class="mt-2 text-xs text-gray-500">
                {% if activity.type == 'crawl' %}
                    {% if activity.details.url %}
                    <span class="mr-4">URL: {{ activity.details.url }}</span>
                    {% endif %}
                    {% if activity.details.pages_crawled %}
                    <span class="mr-4">Pages: {{ activity.details.pages_crawled }}</span>
                    {% endif %}
                    {% if activity.details.duration %}
                    <span>Duration: {{ activity.details.duration }}s</span>
                    {% endif %}
                {% elif activity.type == 'query' %}
                    {% if activity.details.query %}
                    <span class="mr-4">Query: "{{ activity.details.query[:50] }}{% if activity.details.query|length > 50 %}...{% endif %}"</span>
                    {% endif %}
                    {% if activity.details.results_count %}
                    <span class="mr-4">Results: {{ activity.details.results_count }}</span>
                    {% endif %}
                    {% if activity.details.response_time %}
                    <span>Time: {{ (activity.details.response_time * 1000)|round|int }}ms</span>
                    {% endif %}
                {% elif activity.type == 'error' %}
                    {% if activity.details.component %}
                    <span class="mr-4">Component: {{ activity.details.component }}</span>
                    {% endif %}
                    {% if activity.details.error_code %}
                    <span>Code: {{ activity.details.error_code }}</span>
                    {% endif %}
                {% elif activity.type == 'system' %}
                    {% if activity.details.component %}
                    <span class="mr-4">Component: {{ activity.details.component }}</span>
                    {% endif %}
                    {% if activity.details.action %}
                    <span>Action: {{ activity.details.action }}</span>
                    {% endif %}
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
    {% endfor %}
    
    {% if activity_timeline|length > 20 %}
    <div class="text-center py-4">
        <button class="text-sm text-blue-600 hover:text-blue-800 font-medium">
            Load {{ activity_timeline|length - 20 }} more activities
        </button>
    </div>
    {% endif %}
</div>

{% else %}
<!-- No activity data available -->
<div class="text-center py-12">
    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
    </svg>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No Recent Activity</h3>
    <p class="text-gray-600">
        {% if not server_available %}
        Server is not available. Please check the connection.
        {% else %}
        Activity timeline will show recent system operations and events.
        {% endif %}
    </p>
    {% if server_available %}
    <div class="mt-6 space-x-4">
        <a href="/crawl" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
            </svg>
            Start Crawling
        </a>
        <a href="/query" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            Run Query
        </a>
    </div>
    {% endif %}
</div>
{% endif %}