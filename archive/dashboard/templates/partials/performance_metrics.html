{% if server_available and performance_metrics %}
<!-- Query Performance -->
<div class="mb-8">
    <h4 class="text-md font-semibold text-gray-800 mb-4">Query Performance</h4>
    <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="bg-blue-50 rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-blue-800">Average Response</p>
                    <p class="text-xl font-bold text-blue-900">{{ (performance_metrics.query_response_times.avg * 1000)|round|int }}ms</p>
                </div>
                <div class="p-2 bg-blue-200 rounded-full">
                    <svg class="w-5 h-5 text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
            </div>
        </div>
        
        <div class="bg-green-50 rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-green-800">95th Percentile</p>
                    <p class="text-xl font-bold text-green-900">{{ (performance_metrics.query_response_times.p95 * 1000)|round|int }}ms</p>
                </div>
                <div class="p-2 bg-green-200 rounded-full">
                    <svg class="w-5 h-5 text-green-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Response Time Range -->
    <div class="bg-gray-50 rounded-lg p-4">
        <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
            <span>Min: {{ (performance_metrics.query_response_times.min * 1000)|round|int }}ms</span>
            <span>Max: {{ (performance_metrics.query_response_times.max * 1000)|round|int }}ms</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-gradient-to-r from-green-500 via-yellow-500 to-red-500 h-2 rounded-full" style="width: 100%"></div>
        </div>
    </div>
</div>

<!-- Crawl Performance -->
{% if performance_metrics.crawl_performance %}
<div class="mb-8">
    <h4 class="text-md font-semibold text-gray-800 mb-4">Crawl Performance</h4>
    <div class="grid grid-cols-3 gap-4">
        <div class="text-center p-4 bg-purple-50 rounded-lg">
            <p class="text-2xl font-bold text-purple-900">{{ performance_metrics.crawl_performance.avg_pages_per_minute|round(1) }}</p>
            <p class="text-sm text-purple-700">Pages/min</p>
        </div>
        
        <div class="text-center p-4 bg-indigo-50 rounded-lg">
            <p class="text-2xl font-bold text-indigo-900">{{ (performance_metrics.crawl_performance.avg_content_size / 1024)|round|int }}KB</p>
            <p class="text-sm text-indigo-700">Avg Size</p>
        </div>
        
        <div class="text-center p-4 bg-teal-50 rounded-lg">
            <p class="text-2xl font-bold text-teal-900">{{ (performance_metrics.crawl_performance.success_rate * 100)|round(1) }}%</p>
            <p class="text-sm text-teal-700">Success Rate</p>
        </div>
    </div>
</div>
{% endif %}

<!-- System Resources -->
{% if performance_metrics.system_resources %}
<div>
    <h4 class="text-md font-semibold text-gray-800 mb-4">System Resources</h4>
    <div class="space-y-4">
        <!-- CPU Usage -->
        <div>
            <div class="flex items-center justify-between mb-1">
                <span class="text-sm font-medium text-gray-700">CPU Usage</span>
                <span class="text-sm text-gray-600">{{ performance_metrics.system_resources.cpu_usage|round(1) }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: {{ performance_metrics.system_resources.cpu_usage }}%"></div>
            </div>
        </div>
        
        <!-- Memory Usage -->
        <div>
            <div class="flex items-center justify-between mb-1">
                <span class="text-sm font-medium text-gray-700">Memory Usage</span>
                <span class="text-sm text-gray-600">{{ performance_metrics.system_resources.memory_usage|round(1) }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-green-600 h-2 rounded-full transition-all duration-300" style="width: {{ performance_metrics.system_resources.memory_usage }}%"></div>
            </div>
        </div>
        
        <!-- Disk Usage -->
        <div>
            <div class="flex items-center justify-between mb-1">
                <span class="text-sm font-medium text-gray-700">Disk Usage</span>
                <span class="text-sm text-gray-600">{{ performance_metrics.system_resources.disk_usage|round(1) }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-purple-600 h-2 rounded-full transition-all duration-300" style="width: {{ performance_metrics.system_resources.disk_usage }}%"></div>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% else %}
<!-- No performance data available -->
<div class="text-center py-8">
    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
    </svg>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No Performance Data</h3>
    <p class="text-gray-600">
        {% if not server_available %}
        Server is not available. Please check the connection.
        {% else %}
        Performance metrics will appear after system activity.
        {% endif %}
    </p>
</div>
{% endif %}