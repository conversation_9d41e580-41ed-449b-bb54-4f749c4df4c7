<!-- Saved Queries Partial -->
{% if saved_queries %}
    {% for query in saved_queries %}
    <div class="flex items-center justify-between p-2 hover:bg-gray-50 rounded-md group">
        <div class="flex-1 min-w-0">
            <button class="text-left w-full text-sm text-gray-700 hover:text-blue-600 truncate"
                    onclick="loadSavedQuery('{{ query.query }}')">
                {% if query.name %}
                    {{ query.name }}
                {% else %}
                    {{ query.query }}
                {% endif %}
            </button>
            <div class="text-xs text-gray-500 mt-1">
                {% if query.name and query.query != query.name %}
                    {{ query.query | truncate(30) }} • 
                {% endif %}
                Saved {{ query.saved_date }}
            </div>
        </div>
        <div class="opacity-0 group-hover:opacity-100 flex items-center space-x-1 ml-2">
            <button class="text-gray-400 hover:text-blue-600"
                    hx-get="/htmx/query/saved/{{ query.id }}/edit"
                    hx-target="#edit-query-modal"
                    hx-swap="innerHTML"
                    title="Edit query">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
            </button>
            <button class="text-gray-400 hover:text-red-600"
                    hx-delete="/htmx/query/saved/{{ query.id }}"
                    hx-target="#saved-queries"
                    hx-confirm="Remove this saved query?"
                    title="Delete query">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    </div>
    {% endfor %}
{% else %}
    <div class="text-sm text-gray-500 text-center py-4">
        No saved queries
    </div>
{% endif %}

<!-- Edit Query Modal Placeholder -->
<div id="edit-query-modal"></div>

<script>
function loadSavedQuery(query) {
    document.getElementById('query').value = query;
    // Optionally trigger search immediately
    document.getElementById('search-form').dispatchEvent(new Event('submit'));
}
</script>