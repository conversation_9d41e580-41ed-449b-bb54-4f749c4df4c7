{% if server_available and error_statistics %}
<!-- Error Overview -->
<div class="grid grid-cols-2 gap-4 mb-6">
    <div class="bg-red-50 rounded-lg p-4 border border-red-200">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-red-800">Total Errors</p>
                <p class="text-2xl font-bold text-red-900">{{ error_statistics.total_errors|default(0) }}</p>
            </div>
            <div class="p-2 bg-red-200 rounded-full">
                <svg class="w-5 h-5 text-red-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
        </div>
    </div>
    
    <div class="bg-orange-50 rounded-lg p-4 border border-orange-200">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-orange-800">Error Rate</p>
                <p class="text-2xl font-bold text-orange-900">{{ (error_statistics.error_rate * 100)|round(2) if error_statistics.error_rate else 0 }}%</p>
            </div>
            <div class="p-2 bg-orange-200 rounded-full">
                <svg class="w-5 h-5 text-orange-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Error Breakdown -->
{% if error_statistics.error_breakdown %}
<div class="mb-6">
    <h4 class="text-md font-semibold text-gray-800 mb-4">Error Breakdown</h4>
    <div class="space-y-3">
        {% for error_type, count in error_statistics.error_breakdown.items() %}
        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex items-center">
                <div class="w-3 h-3 rounded-full mr-3 {% if error_type == 'connection_errors' %}bg-red-500{% elif error_type == 'timeout_errors' %}bg-yellow-500{% elif error_type == 'validation_errors' %}bg-blue-500{% else %}bg-purple-500{% endif %}"></div>
                <span class="font-medium text-gray-900">
                    {% if error_type == 'connection_errors' %}
                    Connection Errors
                    {% elif error_type == 'timeout_errors' %}
                    Timeout Errors
                    {% elif error_type == 'validation_errors' %}
                    Validation Errors
                    {% elif error_type == 'server_errors' %}
                    Server Errors
                    {% else %}
                    {{ error_type|title|replace('_', ' ') }}
                    {% endif %}
                </span>
            </div>
            <div class="flex items-center space-x-3">
                <div class="w-20 bg-gray-200 rounded-full h-2">
                    <div class="h-2 rounded-full {% if error_type == 'connection_errors' %}bg-red-500{% elif error_type == 'timeout_errors' %}bg-yellow-500{% elif error_type == 'validation_errors' %}bg-blue-500{% else %}bg-purple-500{% endif %}" 
                         style="width: {{ (count / error_statistics.total_errors * 100) if error_statistics.total_errors > 0 else 0 }}%"></div>
                </div>
                <span class="text-sm font-medium text-gray-700">{{ count }}</span>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}

<!-- Recent Errors -->
{% if error_statistics.recent_errors %}
<div>
    <h4 class="text-md font-semibold text-gray-800 mb-4">Recent Errors</h4>
    <div class="space-y-2 max-h-64 overflow-y-auto">
        {% for error in error_statistics.recent_errors[:10] %}
        <div class="p-3 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <div class="flex items-center mb-1">
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium {% if error.severity == 'critical' %}bg-red-100 text-red-800{% elif error.severity == 'high' %}bg-orange-100 text-orange-800{% elif error.severity == 'medium' %}bg-yellow-100 text-yellow-800{% else %}bg-blue-100 text-blue-800{% endif %}">
                            {{ error.severity|default('unknown')|title }}
                        </span>
                        <span class="ml-2 text-xs text-gray-500">{{ error.timestamp if error.timestamp else 'Unknown time' }}</span>
                    </div>
                    <p class="text-sm font-medium text-gray-900 mb-1">{{ error.message|default('Unknown error') }}</p>
                    {% if error.details %}
                    <p class="text-xs text-gray-600">{{ error.details }}</p>
                    {% endif %}
                    {% if error.component %}
                    <p class="text-xs text-gray-500 mt-1">Component: {{ error.component }}</p>
                    {% endif %}
                </div>
                {% if error.count and error.count > 1 %}
                <div class="ml-3 flex-shrink-0">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {{ error.count }}x
                    </span>
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
        
        {% if error_statistics.recent_errors|length > 10 %}
        <div class="text-center">
            <button class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                Show {{ error_statistics.recent_errors|length - 10 }} more errors
            </button>
        </div>
        {% endif %}
    </div>
</div>
{% endif %}

<!-- No errors state -->
{% if error_statistics.total_errors == 0 %}
<div class="text-center py-8">
    <svg class="w-12 h-12 text-green-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
    </svg>
    <h3 class="text-lg font-medium text-green-900 mb-2">No Errors Detected</h3>
    <p class="text-green-700">System is running smoothly with no recent errors.</p>
</div>
{% endif %}

{% else %}
<!-- No error data available -->
<div class="text-center py-8">
    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
    </svg>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No Error Data Available</h3>
    <p class="text-gray-600">
        {% if not server_available %}
        Server is not available. Please check the connection.
        {% else %}
        Error statistics will appear when system errors occur.
        {% endif %}
    </p>
</div>
{% endif %}