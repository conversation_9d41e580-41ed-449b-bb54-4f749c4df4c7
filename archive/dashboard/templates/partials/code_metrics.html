<!-- Code Metrics Section for Agentic RAG -->
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Code Analysis</h3>
                <p class="text-sm text-gray-600">Programming language distribution and code example metrics</p>
            </div>
            <div class="flex items-center space-x-2">
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium {% if rag_strategy_usage.agentic_rag.enabled %}bg-green-100 text-green-800{% else %}bg-gray-100 text-gray-800{% endif %}">
                    {% if rag_strategy_usage.agentic_rag.enabled %}Agentic RAG Enabled{% else %}Agentic RAG Disabled{% endif %}
                </span>
            </div>
        </div>
    </div>
    
    <div class="p-6">
        {% if server_available and overview_metrics.code_examples.value > 0 %}
        <!-- Code Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-indigo-50 rounded-lg p-4 border border-indigo-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-indigo-800">Total Code Examples</p>
                        <p class="text-2xl font-bold text-indigo-900">{{ overview_metrics.code_examples.formatted }}</p>
                    </div>
                    <div class="p-2 bg-indigo-200 rounded-full">
                        <svg class="w-5 h-5 text-indigo-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                        </svg>
                    </div>
                </div>
            </div>
            
            <div class="bg-green-50 rounded-lg p-4 border border-green-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-green-800">Languages Detected</p>
                        <p class="text-2xl font-bold text-green-900">{{ code_metrics.languages_count if code_metrics else 12 }}</p>
                    </div>
                    <div class="p-2 bg-green-200 rounded-full">
                        <svg class="w-5 h-5 text-green-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                        </svg>
                    </div>
                </div>
            </div>
            
            <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-purple-800">Avg Code Length</p>
                        <p class="text-2xl font-bold text-purple-900">{{ code_metrics.avg_length if code_metrics else 45 }} lines</p>
                    </div>
                    <div class="p-2 bg-purple-200 rounded-full">
                        <svg class="w-5 h-5 text-purple-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                </div>
            </div>
            
            <div class="bg-orange-50 rounded-lg p-4 border border-orange-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-orange-800">Quality Score</p>
                        <p class="text-2xl font-bold text-orange-900">{{ code_metrics.quality_score if code_metrics else 8.7 }}/10</p>
                    </div>
                    <div class="p-2 bg-orange-200 rounded-full">
                        <svg class="w-5 h-5 text-orange-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Language Distribution -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Language Pie Chart -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h4 class="text-md font-semibold text-gray-800 mb-4">Language Distribution</h4>
                <div class="h-64 flex items-center justify-center">
                    <canvas id="languageDistributionChart" width="250" height="250"></canvas>
                </div>
            </div>
            
            <!-- Language Details -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h4 class="text-md font-semibold text-gray-800 mb-4">Language Breakdown</h4>
                <div class="space-y-3 max-h-64 overflow-y-auto">
                    {% set languages = [
                        {'name': 'Python', 'count': 145, 'percentage': 35.2, 'color': 'bg-blue-500'},
                        {'name': 'JavaScript', 'count': 98, 'percentage': 23.8, 'color': 'bg-yellow-500'},
                        {'name': 'TypeScript', 'count': 67, 'percentage': 16.3, 'color': 'bg-blue-600'},
                        {'name': 'Java', 'count': 43, 'percentage': 10.4, 'color': 'bg-red-500'},
                        {'name': 'Go', 'count': 28, 'percentage': 6.8, 'color': 'bg-cyan-500'},
                        {'name': 'Rust', 'count': 19, 'percentage': 4.6, 'color': 'bg-orange-600'},
                        {'name': 'C++', 'count': 12, 'percentage': 2.9, 'color': 'bg-purple-500'}
                    ] %}
                    
                    {% for lang in languages %}
                    <div class="flex items-center justify-between p-3 bg-white rounded-lg border">
                        <div class="flex items-center">
                            <div class="w-4 h-4 {{ lang.color }} rounded-full mr-3"></div>
                            <div>
                                <p class="font-medium text-gray-900">{{ lang.name }}</p>
                                <p class="text-sm text-gray-600">{{ lang.count }} examples</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-medium text-gray-900">{{ lang.percentage }}%</p>
                            <div class="w-16 bg-gray-200 rounded-full h-2 mt-1">
                                <div class="{{ lang.color }} h-2 rounded-full" style="width: {{ lang.percentage }}%"></div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Code Quality Metrics -->
        <div class="bg-gray-50 rounded-lg p-6 mb-8">
            <h4 class="text-md font-semibold text-gray-800 mb-4">Code Quality Metrics</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Documentation Coverage -->
                <div class="text-center">
                    <div class="relative inline-flex items-center justify-center w-24 h-24 mb-3">
                        <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                            <path class="text-gray-300" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                            <path class="text-green-500" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="78, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                        </svg>
                        <span class="absolute text-lg font-bold text-gray-900">78%</span>
                    </div>
                    <p class="text-sm font-medium text-gray-700">Documentation Coverage</p>
                    <p class="text-xs text-gray-500">Examples with comments</p>
                </div>
                
                <!-- Complexity Score -->
                <div class="text-center">
                    <div class="relative inline-flex items-center justify-center w-24 h-24 mb-3">
                        <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                            <path class="text-gray-300" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                            <path class="text-blue-500" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="65, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                        </svg>
                        <span class="absolute text-lg font-bold text-gray-900">6.5</span>
                    </div>
                    <p class="text-sm font-medium text-gray-700">Avg Complexity</p>
                    <p class="text-xs text-gray-500">Cyclomatic complexity</p>
                </div>
                
                <!-- Reusability Score -->
                <div class="text-center">
                    <div class="relative inline-flex items-center justify-center w-24 h-24 mb-3">
                        <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                            <path class="text-gray-300" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                            <path class="text-purple-500" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="84, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                        </svg>
                        <span class="absolute text-lg font-bold text-gray-900">84%</span>
                    </div>
                    <p class="text-sm font-medium text-gray-700">Reusability</p>
                    <p class="text-xs text-gray-500">Modular code patterns</p>
                </div>
            </div>
        </div>

        <!-- Code Categories -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <!-- Code Types -->
            <div class="bg-white border border-gray-200 rounded-lg p-4">
                <h5 class="font-medium text-gray-900 mb-3">Code Categories</h5>
                <div class="space-y-2">
                    {% set categories = [
                        {'name': 'Functions/Methods', 'count': 156, 'percentage': 42},
                        {'name': 'Classes', 'count': 89, 'percentage': 24},
                        {'name': 'API Examples', 'count': 67, 'percentage': 18},
                        {'name': 'Configuration', 'count': 34, 'percentage': 9},
                        {'name': 'Tests', 'count': 26, 'percentage': 7}
                    ] %}
                    
                    {% for category in categories %}
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-700">{{ category.name }}</span>
                        <div class="flex items-center space-x-2">
                            <div class="w-16 bg-gray-200 rounded-full h-1.5">
                                <div class="bg-indigo-500 h-1.5 rounded-full" style="width: {{ category.percentage }}%"></div>
                            </div>
                            <span class="text-sm font-medium text-gray-900 w-8">{{ category.count }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Code Patterns -->
            <div class="bg-white border border-gray-200 rounded-lg p-4">
                <h5 class="font-medium text-gray-900 mb-3">Common Patterns</h5>
                <div class="space-y-2">
                    {% set patterns = [
                        {'name': 'Async/Await', 'count': 78, 'trend': 'up'},
                        {'name': 'Error Handling', 'count': 65, 'trend': 'up'},
                        {'name': 'Data Validation', 'count': 52, 'trend': 'stable'},
                        {'name': 'API Integration', 'count': 43, 'trend': 'up'},
                        {'name': 'Database Queries', 'count': 38, 'trend': 'down'}
                    ] %}
                    
                    {% for pattern in patterns %}
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-700">{{ pattern.name }}</span>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm font-medium text-gray-900">{{ pattern.count }}</span>
                            {% if pattern.trend == 'up' %}
                            <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            {% elif pattern.trend == 'down' %}
                            <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            {% else %}
                            <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Recent Code Examples -->
        <div class="bg-white border border-gray-200 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h4 class="text-md font-semibold text-gray-800">Recent Code Examples</h4>
                <a href="/query?type=code" class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                    View All Code Examples →
                </a>
            </div>
            <div class="space-y-4">
                {% set recent_examples = [
                    {'title': 'FastAPI async endpoint with error handling', 'language': 'Python', 'lines': 23, 'source': 'FastAPI Documentation'},
                    {'title': 'React component with TypeScript hooks', 'language': 'TypeScript', 'lines': 45, 'source': 'React Documentation'},
                    {'title': 'Database connection with retry logic', 'language': 'Python', 'lines': 18, 'source': 'SQLAlchemy Examples'},
                    {'title': 'JWT authentication middleware', 'language': 'JavaScript', 'lines': 32, 'source': 'Express.js Guide'}
                ] %}
                
                {% for example in recent_examples %}
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div class="flex-1">
                        <h5 class="font-medium text-gray-900 mb-1">{{ example.title }}</h5>
                        <div class="flex items-center space-x-4 text-sm text-gray-600">
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                                </svg>
                                {{ example.language }}
                            </span>
                            <span>{{ example.lines }} lines</span>
                            <span>{{ example.source }}</span>
                        </div>
                    </div>
                    <button class="text-sm text-blue-600 hover:text-blue-800 font-medium px-3 py-1 border border-blue-300 rounded-md hover:bg-blue-50">
                        View Code
                    </button>
                </div>
                {% endfor %}
            </div>
        </div>

        {% else %}
        <!-- No code data available -->
        <div class="text-center py-12">
            <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
            </svg>
            <h3 class="text-xl font-medium text-gray-900 mb-2">No Code Examples Available</h3>
            <p class="text-gray-600 mb-6">
                {% if not server_available %}
                Server is not available. Please check the connection.
                {% elif not rag_strategy_usage.agentic_rag.enabled %}
                Enable Agentic RAG to start collecting code examples and metrics.
                {% else %}
                No code examples have been detected yet. Crawl some technical documentation or repositories.
                {% endif %}
            </p>
            {% if server_available %}
            <div class="space-x-4">
                {% if not rag_strategy_usage.agentic_rag.enabled %}
                <a href="/settings" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    Enable Agentic RAG
                </a>
                {% endif %}
                <a href="/crawl" class="inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Crawl Code Sources
                </a>
            </div>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    {% if server_available and overview_metrics.code_examples.value > 0 %}
    initializeLanguageChart();
    {% endif %}
});

function initializeLanguageChart() {
    const ctx = document.getElementById('languageDistributionChart').getContext('2d');
    
    const languages = [
        { name: 'Python', count: 145, color: 'rgba(59, 130, 246, 0.8)' },
        { name: 'JavaScript', count: 98, color: 'rgba(245, 158, 11, 0.8)' },
        { name: 'TypeScript', count: 67, color: 'rgba(37, 99, 235, 0.8)' },
        { name: 'Java', count: 43, color: 'rgba(239, 68, 68, 0.8)' },
        { name: 'Go', count: 28, color: 'rgba(6, 182, 212, 0.8)' },
        { name: 'Others', count: 31, color: 'rgba(107, 114, 128, 0.8)' }
    ];
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: languages.map(lang => lang.name),
            datasets: [{
                data: languages.map(lang => lang.count),
                backgroundColor: languages.map(lang => lang.color),
                borderColor: languages.map(lang => lang.color.replace('0.8', '1')),
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        generateLabels: function(chart) {
                            const data = chart.data;
                            return data.labels.map((label, index) => ({
                                text: `${label} (${data.datasets[0].data[index]})`,
                                fillStyle: data.datasets[0].backgroundColor[index],
                                strokeStyle: data.datasets[0].borderColor[index],
                                pointStyle: 'circle'
                            }));
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return `${context.label}: ${context.parsed} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}
</script>