<!-- Overview Metrics Cards with Enhanced Error Handling -->
{% if server_available %}
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6" id="metrics-container">
    <!-- Total Documents -->
    <div class="firebase-metric-card border-l-4 border-firebase-orange">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-secondary">Total Documents</p>
                <p class="firebase-metric-value">{{ overview_metrics.total_documents.formatted if overview_metrics.total_documents.value is defined else '0' }}</p>
                {% if overview_metrics.total_documents.trend %}
                <div class="flex items-center mt-1">
                    {% if overview_metrics.total_documents.trend.direction == 'up' %}
                    <svg class="w-4 h-4 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm text-green-600">+{{ overview_metrics.total_documents.trend.percentage }}%</span>
                    {% elif overview_metrics.total_documents.trend.direction == 'down' %}
                    <svg class="w-4 h-4 text-red-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm text-red-600">-{{ overview_metrics.total_documents.trend.percentage }}%</span>
                    {% endif %}
                </div>
                {% endif %}
            </div>
            <div class="p-3 bg-blue-100 rounded-full">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Total Sources -->
    <div class="firebase-metric-card border-l-4 border-green-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-secondary">Sources</p>
                <p class="firebase-metric-value">{{ overview_metrics.total_sources.formatted if overview_metrics.total_sources.value is defined else '0' }}</p>
                {% if overview_metrics.total_sources.trend %}
                <div class="flex items-center mt-1">
                    {% if overview_metrics.total_sources.trend.direction == 'up' %}
                    <svg class="w-4 h-4 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm text-green-600">+{{ overview_metrics.total_sources.trend.percentage }}%</span>
                    {% elif overview_metrics.total_sources.trend.direction == 'down' %}
                    <svg class="w-4 h-4 text-red-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm text-red-600">-{{ overview_metrics.total_sources.trend.percentage }}%</span>
                    {% endif %}
                </div>
                {% endif %}
            </div>
            <div class="p-3 bg-green-100 rounded-full">
                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Average Query Time -->
    <div class="firebase-metric-card border-l-4 border-yellow-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-secondary">Avg Query Time</p>
                <p class="firebase-metric-value">{{ overview_metrics.avg_query_time.formatted if overview_metrics.avg_query_time.value is defined else '0ms' }}</p>
                {% if overview_metrics.avg_query_time.trend %}
                <div class="flex items-center mt-1">
                    {% if overview_metrics.avg_query_time.trend.direction == 'down' %}
                    <svg class="w-4 h-4 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm text-green-600">-{{ overview_metrics.avg_query_time.trend.percentage }}%</span>
                    {% elif overview_metrics.avg_query_time.trend.direction == 'up' %}
                    <svg class="w-4 h-4 text-red-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm text-red-600">+{{ overview_metrics.avg_query_time.trend.percentage }}%</span>
                    {% endif %}
                </div>
                {% endif %}
            </div>
            <div class="p-3 bg-yellow-100 rounded-full">
                <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Storage Usage -->
    <div class="firebase-metric-card border-l-4 border-purple-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-secondary">Storage Usage</p>
                <p class="firebase-metric-value">{{ overview_metrics.storage_usage.formatted if overview_metrics.storage_usage.value is defined else '0 B' }}</p>
                {% if overview_metrics.storage_usage.trend %}
                <div class="flex items-center mt-1">
                    {% if overview_metrics.storage_usage.trend.direction == 'up' %}
                    <svg class="w-4 h-4 text-blue-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm text-blue-600">+{{ overview_metrics.storage_usage.trend.percentage }}%</span>
                    {% elif overview_metrics.storage_usage.trend.direction == 'down' %}
                    <svg class="w-4 h-4 text-red-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm text-red-600">-{{ overview_metrics.storage_usage.trend.percentage }}%</span>
                    {% endif %}
                </div>
                {% endif %}
            </div>
            <div class="p-3 bg-purple-100 rounded-full">
                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Additional Metrics Row -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
    <!-- Total Chunks -->
    <div class="firebase-metric-card border-l-4 border-indigo-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-secondary">Text Chunks</p>
                <p class="firebase-metric-value" style="font-size: 1.5rem;">{{ overview_metrics.total_chunks.formatted if overview_metrics.total_chunks.value is defined else '0' }}</p>
            </div>
            <div class="p-2 bg-indigo-100 rounded-full">
                <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Code Examples -->
    <div class="firebase-metric-card border-l-4 border-pink-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-secondary">Code Examples</p>
                <p class="firebase-metric-value" style="font-size: 1.5rem;">{{ overview_metrics.code_examples.formatted if overview_metrics.code_examples.value is defined else '0' }}</p>
            </div>
            <div class="p-2 bg-pink-100 rounded-full">
                <svg class="w-5 h-5 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Success Rate -->
    <div class="firebase-metric-card border-l-4 border-green-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-secondary">Success Rate</p>
                <p class="firebase-metric-value" style="font-size: 1.5rem;">{{ overview_metrics.crawl_success_rate.formatted if overview_metrics.crawl_success_rate.value is defined else '0%' }}</p>
            </div>
            <div class="p-2 bg-green-100 rounded-full">
                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Active Connections -->
    <div class="firebase-metric-card border-l-4 border-orange-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-secondary">Active Connections</p>
                <p class="firebase-metric-value" style="font-size: 1.5rem;">{{ overview_metrics.active_connections.formatted if overview_metrics.active_connections.value is defined else '0' }}</p>
            </div>
            <div class="p-2 bg-orange-100 rounded-full">
                <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"></path>
                </svg>
            </div>
        </div>
    </div>
</div>

{% else %}
<!-- Server Unavailable State -->
<div class="error-boundary" role="alert" aria-live="assertive">
    <div class="flex">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
            </svg>
        </div>
        <div class="ml-3 flex-1">
            <h3 class="text-sm font-medium text-red-800">Service Unavailable</h3>
            <div class="mt-2 text-sm text-red-700">
                <p>Unable to connect to the database service. Metrics are temporarily unavailable.</p>
            </div>
            <div class="mt-3">
                <button 
                    class="btn btn-sm btn-secondary"
                    hx-get="/stats/htmx/overview-metrics"
                    hx-target="#metrics-container"
                    hx-indicator=".loading-indicator"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    Retry Connection
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Loading Skeleton for Retry -->
<div class="loading-indicator htmx-indicator">
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {% for i in range(4) %}
        <div class="card-loading">
            <div class="skeleton skeleton-text w-1-2"></div>
            <div class="skeleton skeleton-text w-3-4"></div>
            <div class="skeleton skeleton-text w-1-4"></div>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}