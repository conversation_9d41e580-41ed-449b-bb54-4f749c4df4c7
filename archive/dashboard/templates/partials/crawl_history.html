<!-- Crawl History Partial -->
<div class="space-y-3">
    {% if crawl_history %}
        {% for crawl in crawl_history %}
        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors">
            <div class="flex-1 min-w-0">
                <p class="font-medium text-gray-900 truncate">{{ crawl.url }}</p>
                <div class="flex items-center space-x-4 mt-1">
                    <p class="text-sm text-gray-600">{{ crawl.timestamp }}</p>
                    <p class="text-sm text-gray-600">{{ "%.1f"|format(crawl.duration) }}s</p>
                </div>
            </div>
            <div class="flex items-center space-x-3 flex-shrink-0">
                {% if crawl.success %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    Success
                </span>
                <span class="text-sm text-gray-600">{{ crawl.chunks_stored }} chunks</span>
                {% else %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    Failed
                </span>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    {% else %}
    <div class="text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No crawl history</h3>
        <p class="mt-1 text-sm text-gray-500">Start your first crawl operation to see history here.</p>
    </div>
    {% endif %}
</div>