{% if error %}
    <div class="text-sm text-red-600">{{ error }}</div>
{% elif audit_entries %}
    <div class="space-y-3">
        {% for entry in audit_entries %}
            <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                <div class="flex-shrink-0 mt-0.5">
                    {% if entry.success %}
                        <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                    {% else %}
                        <div class="w-2 h-2 bg-red-400 rounded-full"></div>
                    {% endif %}
                </div>
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                        <p class="text-sm font-medium text-gray-900">{{ entry.change_type }}</p>
                        <p class="text-xs text-gray-500">{{ entry.time_ago }}</p>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">{{ entry.summary }}</p>
                    <p class="text-xs text-gray-400 mt-1">{{ entry.timestamp[:19] }}</p>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-sm text-gray-500">No configuration changes recorded yet.</div>
{% endif %}