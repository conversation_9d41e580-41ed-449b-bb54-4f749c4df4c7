<!-- Source Analysis Section with Detailed Breakdown -->
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Source Analysis</h3>
                <p class="text-sm text-gray-600">Detailed breakdown of content sources and their performance</p>
            </div>
            <div class="flex items-center space-x-2">
                <select class="text-sm border-gray-300 rounded-md" id="source-analysis-view">
                    <option value="documents">By Documents</option>
                    <option value="size">By Size</option>
                    <option value="activity">By Activity</option>
                </select>
                <button class="text-sm text-blue-600 hover:text-blue-800 font-medium" onclick="refreshSourceAnalysis()">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Refresh
                </button>
            </div>
        </div>
    </div>
    
    <div class="p-6">
        {% if server_available and content_breakdown and content_breakdown.by_source %}
        <!-- Source Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-blue-800">Total Sources</p>
                        <p class="text-2xl font-bold text-blue-900">{{ content_breakdown.by_source|length }}</p>
                    </div>
                    <div class="p-2 bg-blue-200 rounded-full">
                        <svg class="w-5 h-5 text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                        </svg>
                    </div>
                </div>
            </div>
            
            <div class="bg-green-50 rounded-lg p-4 border border-green-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-green-800">Most Active</p>
                        <p class="text-lg font-bold text-green-900 truncate">{{ content_breakdown.by_source[0].name if content_breakdown.by_source else 'N/A' }}</p>
                    </div>
                    <div class="p-2 bg-green-200 rounded-full">
                        <svg class="w-5 h-5 text-green-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                </div>
            </div>
            
            <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-purple-800">Total Documents</p>
                        <p class="text-2xl font-bold text-purple-900">{{ content_breakdown.by_source|sum(attribute='document_count') }}</p>
                    </div>
                    <div class="p-2 bg-purple-200 rounded-full">
                        <svg class="w-5 h-5 text-purple-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Source Distribution Chart -->
        <div class="mb-8">
            <h4 class="text-md font-semibold text-gray-800 mb-4">Source Distribution</h4>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Pie Chart -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h5 class="text-sm font-medium text-gray-700 mb-3">Document Distribution</h5>
                    <div class="h-64 flex items-center justify-center">
                        <canvas id="sourceDistributionChart" width="250" height="250"></canvas>
                    </div>
                </div>
                
                <!-- Bar Chart -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h5 class="text-sm font-medium text-gray-700 mb-3">Documents per Source</h5>
                    <div class="h-64">
                        <canvas id="sourceBarChart" width="300" height="250"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Source List -->
        <div class="space-y-4">
            <h4 class="text-md font-semibold text-gray-800">Source Details</h4>
            
            {% for source in content_breakdown.by_source %}
            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <h5 class="font-medium text-gray-900 mr-3">{{ source.name }}</h5>
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                {{ source.document_count }} docs
                            </span>
                            {% if source.last_crawl %}
                            <span class="ml-2 text-xs text-gray-500">
                                Last crawled: {{ source.last_crawl }}
                            </span>
                            {% endif %}
                        </div>
                        
                        <!-- Source URL/Domain -->
                        {% if source.url %}
                        <p class="text-sm text-gray-600 mb-2">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.1a3 3 0 105.656-5.656l-2.829-2.829a3 3 0 00-3.536 0z"></path>
                            </svg>
                            <a href="{{ source.url }}" target="_blank" class="text-blue-600 hover:text-blue-800">{{ source.url }}</a>
                        </p>
                        {% endif %}
                        
                        <!-- Source Summary -->
                        {% if source.summary %}
                        <p class="text-sm text-gray-700 mb-3">{{ source.summary }}</p>
                        {% endif %}
                        
                        <!-- Source Metrics -->
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">Documents:</span>
                                <span class="font-medium text-gray-900 ml-1">{{ source.document_count }}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Avg Size:</span>
                                <span class="font-medium text-gray-900 ml-1">{{ (source.avg_size / 1024)|round|int if source.avg_size else 0 }}KB</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Success Rate:</span>
                                <span class="font-medium {% if source.success_rate >= 0.9 %}text-green-600{% elif source.success_rate >= 0.7 %}text-yellow-600{% else %}text-red-600{% endif %} ml-1">
                                    {{ (source.success_rate * 100)|round(1) if source.success_rate else 0 }}%
                                </span>
                            </div>
                            <div>
                                <span class="text-gray-600">Code Examples:</span>
                                <span class="font-medium text-gray-900 ml-1">{{ source.code_examples_count if source.code_examples_count else 0 }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Source Actions -->
                    <div class="flex items-center space-x-2 ml-4">
                        <button class="text-sm text-blue-600 hover:text-blue-800 font-medium px-3 py-1 border border-blue-300 rounded-md hover:bg-blue-50">
                            View Details
                        </button>
                        <button class="text-sm text-green-600 hover:text-green-800 font-medium px-3 py-1 border border-green-300 rounded-md hover:bg-green-50">
                            Re-crawl
                        </button>
                    </div>
                </div>
                
                <!-- Progress Bar for Document Count -->
                <div class="mt-3">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                             style="width: {{ (source.document_count / content_breakdown.by_source[0].document_count * 100) if content_breakdown.by_source[0].document_count > 0 else 0 }}%"></div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Source Performance Trends -->
        <div class="mt-8 bg-gray-50 rounded-lg p-6">
            <h4 class="text-md font-semibold text-gray-800 mb-4">Source Performance Trends</h4>
            <div class="h-64">
                <canvas id="sourcePerformanceChart" width="800" height="250"></canvas>
            </div>
        </div>

        {% else %}
        <!-- No source data available -->
        <div class="text-center py-12">
            <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            <h3 class="text-xl font-medium text-gray-900 mb-2">No Source Data Available</h3>
            <p class="text-gray-600 mb-6">
                {% if not server_available %}
                Server is not available. Please check the connection.
                {% else %}
                No content sources have been crawled yet. Start by adding some sources to see detailed analysis.
                {% endif %}
            </p>
            {% if server_available %}
            <div class="space-x-4">
                <a href="/crawl" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Start Crawling
                </a>
                <a href="/content" class="inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    Manage Content
                </a>
            </div>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    {% if server_available and content_breakdown and content_breakdown.by_source %}
    initializeSourceCharts();
    {% endif %}
});

function initializeSourceCharts() {
    // Source Distribution Pie Chart
    const pieCtx = document.getElementById('sourceDistributionChart').getContext('2d');
    const sourceData = {{ content_breakdown.by_source|tojson if content_breakdown and content_breakdown.by_source else '[]' }};
    
    const pieLabels = sourceData.slice(0, 6).map(source => source.name);
    const pieData = sourceData.slice(0, 6).map(source => source.document_count);
    const pieColors = [
        'rgba(59, 130, 246, 0.8)',
        'rgba(34, 197, 94, 0.8)',
        'rgba(239, 68, 68, 0.8)',
        'rgba(245, 158, 11, 0.8)',
        'rgba(168, 85, 247, 0.8)',
        'rgba(236, 72, 153, 0.8)'
    ];
    
    new Chart(pieCtx, {
        type: 'doughnut',
        data: {
            labels: pieLabels,
            datasets: [{
                data: pieData,
                backgroundColor: pieColors,
                borderColor: pieColors.map(color => color.replace('0.8', '1')),
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
    
    // Source Bar Chart
    const barCtx = document.getElementById('sourceBarChart').getContext('2d');
    
    new Chart(barCtx, {
        type: 'bar',
        data: {
            labels: pieLabels,
            datasets: [{
                label: 'Documents',
                data: pieData,
                backgroundColor: 'rgba(59, 130, 246, 0.8)',
                borderColor: 'rgb(59, 130, 246)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Document Count'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // Source Performance Trends Chart
    const perfCtx = document.getElementById('sourcePerformanceChart').getContext('2d');
    
    // Check if performance metrics are available
    const performanceMetrics = {{ performance_metrics|tojson if performance_metrics else 'null' }};
    
    let days = [];
    let datasets = [];
    
    if (performanceMetrics && performanceMetrics.source_trends) {
        // Use real performance data from server
        const sourceTrends = performanceMetrics.source_trends;
        days = sourceTrends.labels || ['No Data'];
        
        datasets = sourceTrends.datasets || [];
    } else {
        // No performance data available - show error state
        perfCtx.canvas.parentElement.innerHTML = '<div class="flex items-center justify-center h-full text-gray-500"><p>No source performance data available. Server may be disconnected.</p></div>';
        return;
    }
    
    new Chart(perfCtx, {
        type: 'line',
        data: {
            labels: days,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Documents Added'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Date'
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

function refreshSourceAnalysis() {
    // Trigger HTMX refresh for source analysis
    htmx.trigger('#source-analysis', 'refresh');
}
</script>