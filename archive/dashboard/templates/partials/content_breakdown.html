{% if server_available and content_breakdown %}
<!-- Content by Source -->
{% if content_breakdown.by_source %}
<div class="mb-8">
    <h4 class="text-md font-semibold text-gray-800 mb-4">Content by Source</h4>
    <div class="space-y-3">
        {% for source in content_breakdown.by_source[:10] %}
        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex-1">
                <p class="font-medium text-gray-900 truncate">{{ source.name }}</p>
                <p class="text-sm text-gray-600">{{ source.document_count }} documents</p>
            </div>
            <div class="flex items-center space-x-3">
                <div class="w-24 bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-600 h-2 rounded-full" style="width: {{ (source.document_count / content_breakdown.by_source[0].document_count * 100) if content_breakdown.by_source[0].document_count > 0 else 0 }}%"></div>
                </div>
                <span class="text-sm font-medium text-gray-700">{{ source.document_count }}</span>
            </div>
        </div>
        {% endfor %}
        
        {% if content_breakdown.by_source|length > 10 %}
        <div class="text-center">
            <button class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                Show {{ content_breakdown.by_source|length - 10 }} more sources
            </button>
        </div>
        {% endif %}
    </div>
</div>
{% endif %}

<!-- Content by Type -->
<div class="mb-8">
    <h4 class="text-md font-semibold text-gray-800 mb-4">Content by Type</h4>
    <div class="grid grid-cols-2 gap-4">
        {% for content_type in content_breakdown.by_type %}
        <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-center justify-between mb-2">
                <h5 class="font-medium text-gray-900">{{ content_type.name }}</h5>
                <span class="text-sm font-medium text-gray-600">{{ content_type.percentage|round(1) }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full" style="width: {{ content_type.percentage }}%"></div>
            </div>
            <p class="text-sm text-gray-600">{{ content_type.count|default(0) }} items</p>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Content by Date (if available) -->
{% if content_breakdown.by_date %}
<div>
    <h4 class="text-md font-semibold text-gray-800 mb-4">Content Added Over Time</h4>
    <div class="space-y-2">
        {% for date_entry in content_breakdown.by_date[-7:] %}
        <div class="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
            <span class="text-sm text-gray-600">{{ date_entry.date }}</span>
            <div class="flex items-center space-x-2">
                <div class="w-16 bg-gray-200 rounded-full h-1.5">
                    <div class="bg-green-500 h-1.5 rounded-full" style="width: {{ (date_entry.count / content_breakdown.by_date|map(attribute='count')|max * 100) if content_breakdown.by_date|map(attribute='count')|max > 0 else 0 }}%"></div>
                </div>
                <span class="text-sm font-medium text-gray-700">{{ date_entry.count }}</span>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}

{% else %}
<!-- No data available -->
<div class="text-center py-8">
    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
    </svg>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No Content Data Available</h3>
    <p class="text-gray-600">
        {% if not server_available %}
        Server is not available. Please check the connection.
        {% else %}
        No content has been crawled yet. Start by adding some sources.
        {% endif %}
    </p>
    {% if server_available %}
    <div class="mt-4">
        <a href="/crawl" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Start Crawling
        </a>
    </div>
    {% endif %}
</div>
{% endif %}