<!-- Search Suggestions Dropdown -->
{% if suggestions %}
<div class="py-1 max-h-60 overflow-y-auto">
    {% for suggestion in suggestions %}
    <button type="button" 
            class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center justify-between suggestion-item"
            onclick="selectSuggestion('{{ suggestion.text }}')">
        <span>{{ suggestion.text }}</span>
        {% if suggestion.count %}
        <span class="text-xs text-gray-500">{{ suggestion.count }} results</span>
        {% endif %}
    </button>
    {% endfor %}
</div>

<script>
function selectSuggestion(text) {
    document.getElementById('query').value = text;
    document.getElementById('search-suggestions').classList.add('hidden');
    // Optionally trigger search immediately
    // document.getElementById('search-form').dispatchEvent(new Event('submit'));
}
</script>
{% else %}
<div class="py-2 px-3 text-sm text-gray-500">
    No suggestions available
</div>
{% endif %}