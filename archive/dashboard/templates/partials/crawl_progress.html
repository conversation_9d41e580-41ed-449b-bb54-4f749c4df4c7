<!-- Crawl Progress Partial -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6" 
     hx-get="/htmx/crawl/status/{{ task_id }}" 
     hx-trigger="every 2s"
     hx-swap="outerHTML">
    
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Crawling in Progress</h3>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {{ status.title() }}
        </span>
    </div>
    
    <div class="space-y-4">
        <!-- URL being crawled -->
        <div>
            <p class="text-sm font-medium text-gray-700">URL:</p>
            <p class="text-sm text-gray-900 break-all">{{ url }}</p>
        </div>
        
        <!-- Progress bar -->
        <div>
            <div class="flex justify-between text-sm text-gray-700 mb-1">
                <span>Progress</span>
                <span>{{ progress }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                     style="width: {{ progress }}%"></div>
            </div>
        </div>
        
        <!-- Status info -->
        <div class="flex items-center justify-between text-sm text-gray-600">
            <span>Started at {{ start_time }}</span>
            <div class="flex items-center">
                <svg class="animate-spin h-4 w-4 text-blue-600 mr-2" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Processing...</span>
            </div>
        </div>
    </div>
</div>