<!-- System Status Indicator -->
<div class="flex items-center text-xs">
    {% if server_status == 'healthy' %}
        <div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
        <span class="text-green-600" data-status="system_status">Connected</span>
    {% elif server_status == 'warning' %}
        <div class="w-2 h-2 bg-yellow-400 rounded-full mr-2"></div>
        <span class="text-yellow-600" data-status="system_status">Warning</span>
    {% elif server_status == 'disconnected' %}
        <div class="w-2 h-2 bg-red-400 rounded-full mr-2"></div>
        <span class="text-red-600" data-status="system_status">Disconnected</span>
    {% else %}
        <div class="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
        <span class="text-gray-600" data-status="system_status">Unknown</span>
    {% endif %}
    
    {% if active_operations > 0 %}
        <span class="ml-2 text-blue-600">({{ active_operations }} active)</span>
    {% endif %}
    
    <div class="ml-auto text-gray-400">
        {{ timestamp | datetime('%H:%M:%S') }}
    </div>
</div>