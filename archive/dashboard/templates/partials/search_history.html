<!-- Search History Partial -->
{% if search_history %}
    {% for search in search_history %}
    <div class="flex items-center justify-between p-2 hover:bg-gray-50 rounded-md group">
        <div class="flex-1 min-w-0">
            <button class="text-left w-full text-sm text-gray-700 hover:text-blue-600 truncate"
                    onclick="loadHistoryQuery('{{ search.query }}')">
                {{ search.query }}
            </button>
            <div class="text-xs text-gray-500 mt-1">
                {{ search.results_count }} results • {{ search.timestamp }}
            </div>
        </div>
        <button class="opacity-0 group-hover:opacity-100 text-gray-400 hover:text-red-600 ml-2"
                hx-delete="/htmx/query/history/{{ search.id }}"
                hx-target="#search-history"
                hx-confirm="Remove this search from history?">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>
    {% endfor %}
    
    {% if search_history|length >= 10 %}
    <div class="text-center pt-2 border-t border-gray-200">
        <button class="text-xs text-blue-600 hover:text-blue-800"
                hx-get="/htmx/query/history?all=true"
                hx-target="#search-history">
            View All History
        </button>
    </div>
    {% endif %}
{% else %}
    <div class="text-sm text-gray-500 text-center py-4">
        No recent searches
    </div>
{% endif %}

<script>
function loadHistoryQuery(query) {
    document.getElementById('query').value = query;
    // Optionally trigger search immediately
    document.getElementById('search-form').dispatchEvent(new Event('submit'));
}
</script>