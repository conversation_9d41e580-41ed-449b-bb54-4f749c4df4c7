<!-- Metrics Update Partial -->
<div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
    <div class="bg-white p-4 rounded-lg shadow-sm border">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Documents</p>
                <p class="text-2xl font-semibold text-gray-900" data-metric="total_documents">{{ total_documents | default(0) }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white p-4 rounded-lg shadow-sm border">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                </svg>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Sources</p>
                <p class="text-2xl font-semibold text-gray-900" data-metric="total_sources">{{ total_sources | default(0) }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white p-4 rounded-lg shadow-sm border">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Avg Query Time</p>
                <p class="text-2xl font-semibold text-gray-900">{{ avg_query_time | duration }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white p-4 rounded-lg shadow-sm border">
        <div class="flex items-center">
            <div class="p-2 {% if server_available %}bg-green-100{% else %}bg-red-100{% endif %} rounded-lg">
                <svg class="w-6 h-6 {% if server_available %}text-green-600{% else %}text-red-600{% endif %}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Status</p>
                <p class="text-lg font-semibold {% if server_available %}text-green-600{% else %}text-red-600{% endif %}">
                    {% if server_available %}Online{% else %}Offline{% endif %}
                </p>
            </div>
        </div>
    </div>
</div>

<div class="text-xs text-gray-500 mt-2 text-right">
    Updated: {{ timestamp | datetime('%H:%M:%S') }}
</div>