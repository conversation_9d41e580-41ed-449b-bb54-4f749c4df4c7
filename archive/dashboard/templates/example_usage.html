<!-- Example usage of the new template components -->
{% extends "base.html" %}
{% from 'components/__init__.html' import card, metric_card, input, button, status_badge, progress_bar %}

{% block title %}Component Examples{% endblock %}
{% block page_title %}Component Examples{% endblock %}

{% block content %}
<div class="space-y-8">
    <!-- Metric Cards Example -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {{ metric_card(
            title="Total Documents",
            value="1,234",
            change="+12%",
            change_type="increase",
            icon='<svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>',
            description="Documents in knowledge base"
        ) }}
        
        {{ metric_card(
            title="Active Sources",
            value="42",
            change="+3",
            change_type="increase",
            icon='<svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path></svg>',
            description="Crawled sources"
        ) }}
        
        {{ metric_card(
            title="Query Response Time",
            value="245ms",
            change="-15ms",
            change_type="decrease",
            icon='<svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>',
            description="Average response time"
        ) }}
        
        {{ metric_card(
            title="Storage Used",
            value="2.4GB",
            change="+0.3GB",
            change_type="increase",
            icon='<svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path></svg>',
            description="Database storage"
        ) }}
    </div>
    
    <!-- Card with Form Example -->
    {% call card(
        title="Search Configuration",
        subtitle="Configure search parameters",
        icon='<svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg>'
    ) %}
        <form class="space-y-4">
            {{ input(
                name="query",
                label="Search Query",
                placeholder="Enter your search query...",
                required=True,
                help_text="Enter keywords to search in the knowledge base"
            ) }}
            
            {{ input(
                name="max_results",
                label="Maximum Results",
                type="number",
                value="10",
                help_text="Number of results to return (1-100)"
            ) }}
            
            <div class="flex justify-end space-x-3">
                {{ button("Reset", variant="secondary") }}
                {{ button("Search", type="submit", variant="primary") }}
            </div>
        </form>
    {% endcall %}
    
    <!-- Status Examples -->
    {% call card(title="System Status") %}
        <div class="space-y-4">
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700">MCP Server</span>
                {{ status_badge("online", "Online") }}
            </div>
            
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700">Database</span>
                {{ status_badge("warning", "Slow") }}
            </div>
            
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700">Search Index</span>
                {{ status_badge("offline", "Rebuilding") }}
            </div>
        </div>
    {% endcall %}
    
    <!-- Progress Bar Example -->
    {% call card(title="Current Operations") %}
        <div class="space-y-4">
            {{ progress_bar(
                value=75,
                label="Crawling Progress",
                color="primary"
            ) }}
            
            {{ progress_bar(
                value=45,
                label="Index Rebuild",
                color="warning"
            ) }}
            
            {{ progress_bar(
                value=100,
                label="Backup Complete",
                color="success"
            ) }}
        </div>
    {% endcall %}
</div>
{% endblock %}