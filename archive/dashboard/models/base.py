"""
Base data models for HTMX RAG Dashboard
"""
from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Dict, Any, List
from enum import Enum


class CrawlStatus(str, Enum):
    """Crawl operation status"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ComponentHealth(str, Enum):
    """Component health status"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class DashboardConfig:
    """Dashboard-specific configuration"""
    refresh_interval: int = 5000  # milliseconds
    max_search_results: int = 50
    enable_real_time: bool = True
    theme: str = "light"
    auto_refresh_stats: bool = True


@dataclass
class CrawlOptions:
    """Crawling operation options"""
    max_depth: int = 3
    max_concurrent: int = 5
    chunk_size: int = 1000
    include_code: bool = True
    min_code_length: int = 50


@dataclass
class CrawlOperation:
    """Represents a crawling operation"""
    id: str
    url: str
    status: CrawlStatus
    progress: float = 0.0
    pages_crawled: int = 0
    chunks_stored: int = 0
    code_examples_found: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None
    options: Optional[CrawlOptions] = None


@dataclass
class SearchFilters:
    """Search filtering options"""
    sources: Optional[List[str]] = None
    content_types: Optional[List[str]] = None
    date_range: Optional[tuple] = None
    min_score: Optional[float] = None


@dataclass
class SearchResult:
    """Individual search result"""
    id: str
    content: str
    source_url: str
    title: str
    relevance_score: float
    rerank_score: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class SearchSession:
    """Represents a search session"""
    id: str
    query: str
    results: List[SearchResult]
    filters: SearchFilters
    execution_time: float
    timestamp: datetime
    result_count: int


@dataclass
class SystemMetrics:
    """System performance metrics"""
    timestamp: datetime
    server_status: str
    total_documents: int
    total_sources: int
    storage_usage: int
    avg_query_time: float
    crawl_success_rate: float
    active_crawls: int
    component_health: Dict[str, ComponentHealth]