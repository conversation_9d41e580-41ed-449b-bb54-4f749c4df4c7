# AY RAG MCP Server + HTMX Dashboard Environment Configuration

# =================================================================
# MCP SERVER CONFIGURATION (Required for ay-rag-mcp container)
# =================================================================

# Core MCP Configuration
HOST=0.0.0.0
PORT=8051
TRANSPORT=sse

# Docker Integration
CRAWL4AI_DOCKER_HOST=http://crawl4ai:11235

# =================================================================
# LLM PROVIDER CONFIGURATION
# =================================================================

# Provider Selection (openai or openrouter)
LLM_PROVIDER=openrouter

# API Credentials (REQUIRED - server will crash without valid values)
OPENAI_API_KEY=your-openai-api-key
OPENROUTER_API_KEY=your-openrouter-api-key

# OpenRouter App Identification (Optional but recommended)
OPENROUTER_APP_NAME=AY RAG MCP Server
OPENROUTER_APP_URL=https://github.com/user/mcp-crawl4ai-rag

# =================================================================
# MODEL SELECTION (3 specialized models - OpenRouter optimized)
# =================================================================

# Contextual Embedding: Document comprehension and summarization
# Recommended: Fast, cost-effective model for contextual understanding
CONTEXTUAL_EMBEDDING_MODEL=anthropic/claude-3-haiku
CONTEXTUAL_EMBEDDING_MAX_TOKENS=200
CONTEXTUAL_EMBEDDING_TEMPERATURE=0.3

# Code Analysis: Code extraction and understanding  
# Recommended: Strong code analysis capabilities
CODE_ANALYSIS_MODEL=openai/gpt-4o-mini
CODE_ANALYSIS_MAX_TOKENS=1000
CODE_ANALYSIS_TEMPERATURE=0.1

# Query Enhancement: Search query improvement
# Recommended: Fast, efficient for query processing
QUERY_ENHANCEMENT_MODEL=meta-llama/llama-3.1-8b-instruct
QUERY_ENHANCEMENT_MAX_TOKENS=100
QUERY_ENHANCEMENT_TEMPERATURE=0.2

# Legacy support (used if above models not configured)
MODEL_CHOICE=gpt-4o-mini

# =================================================================
# ALTERNATIVE MODEL CONFIGURATIONS
# =================================================================

# Cost-Optimized Setup (uncomment to use):
# CONTEXTUAL_EMBEDDING_MODEL=meta-llama/llama-3.1-8b-instruct
# CODE_ANALYSIS_MODEL=openai/gpt-4o-mini  
# QUERY_ENHANCEMENT_MODEL=meta-llama/llama-3.1-8b-instruct

# Quality-Optimized Setup (uncomment to use):
# CONTEXTUAL_EMBEDDING_MODEL=anthropic/claude-3-sonnet
# CODE_ANALYSIS_MODEL=openai/gpt-4o
# QUERY_ENHANCEMENT_MODEL=anthropic/claude-3-haiku

# OpenAI-Only Setup (uncomment to use):
# LLM_PROVIDER=openai
# CONTEXTUAL_EMBEDDING_MODEL=gpt-4o-mini
# CODE_ANALYSIS_MODEL=gpt-4o-mini
# QUERY_ENHANCEMENT_MODEL=gpt-3.5-turbo

# Supabase Configuration (REQUIRED - server will crash without valid values)  
SUPABASE_URL=your-supabase-project-url
SUPABASE_SERVICE_KEY=your-supabase-service-key

# RAG Strategy Flags
USE_CONTEXTUAL_EMBEDDINGS=false
USE_HYBRID_SEARCH=true
USE_AGENTIC_RAG=false
USE_RERANKING=false

# Code Detection Configuration
MIN_CODE_LENGTH=50

# GitHub Integration (Optional)
GITHUB_TOKEN=your-github-token

# Resilience Settings
CIRCUIT_BREAKER_THRESHOLD=3
CIRCUIT_BREAKER_TIMEOUT=60
USE_QUERY_EXPANSION=true

# =================================================================
# OPTIONAL INTEGRATIONS
# =================================================================

# Redis Configuration (Optional)
REDIS_URL=redis://redis:6379
REDIS_TIMEOUT=5
ENABLE_REDIS_RATE_LIMITING=false

# Monitoring and Observability
ENABLE_METRICS=true
METRICS_PORT=9090
SENTRY_DSN=your-sentry-dsn-if-using

# Development Settings
RELOAD_ON_CHANGE=false
ENABLE_DOCS=true

# =================================================================
# PERFORMANCE SETTINGS
# =================================================================

# Batch size for processing operations
DEFAULT_BATCH_SIZE=50